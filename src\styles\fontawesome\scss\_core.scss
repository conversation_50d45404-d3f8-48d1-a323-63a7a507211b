// base icon class definition
// -------------------------
@use 'variables' as v;
@use 'mixins' as m;

.#{v.$css-prefix}-solid,
.#{v.$css-prefix}-regular,
.#{v.$css-prefix}-brands,
.#{v.$css-prefix}-classic,
.fas,
.far,
.fab,
.#{v.$css-prefix} {
  @include m.fa-icon();
}

:is(
  .fas,
  .far,
  .fab,
  .#{v.$css-prefix}-solid,
  .#{v.$css-prefix}-regular,
  .#{v.$css-prefix}-brands,
  .#{v.$css-prefix}-classic,
  .fa
)::before {
  content: var(#{v.$icon-property})/"";
}

@supports not (content: ''/'') {
:is(
  .fas,
  .far,
  .fab,
  .#{v.$css-prefix}-solid,
  .#{v.$css-prefix}-regular,
  .#{v.$css-prefix}-brands,
  .#{v.$css-prefix}-classic,
  .fa
)::before {
    content: var(#{v.$icon-property});
  }
}