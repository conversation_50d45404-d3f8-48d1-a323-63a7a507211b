import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import api from "../../services/api";
import { ArrowLeft, Plus, Trash2 } from "lucide-react";
import { <PERSON><PERSON><PERSON>oader } from "react-spinners";
import "../../styles/question-edit.css";
const defaultOption = () => ({
  option_text: "",
  direction: "ltr",
  is_correct: false,
});

const QuestionEdit = () => {
  const navigate = useNavigate();
  const { id } = useParams();

  const [subjects, setSubjects] = useState([]);
  const [form, setForm] = useState({
    subject_id: "",
    question_text: "",
    direction: "ltr",
    type: "mcq",
    options: [
      defaultOption(),
      defaultOption(),
      defaultOption(),
      defaultOption(),
    ],
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const load = async () => {
      try {
        const [subjectsRes, questionRes] = await Promise.all([
          api.get("/admin/subjects"),
          api.get(`/admin/questions/${id}`),
        ]);
        setSubjects(subjectsRes.data.data || subjectsRes.data || []);
        const q = questionRes.data;
        setForm({
          subject_id: q.subject_id,
          question_text: q.question_text || "",
          direction: q.direction || "ltr",
          type: q.type || "mcq",
          options: (q.options || []).map((o) => ({
            option_text: o.option_text || "",
            direction: o.direction || "ltr",
            is_correct: !!o.is_correct,
          })),
        });
      } catch (e) {
        console.error(e);
        setError("Failed to load question");
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [id]);

  const isTrueFalse = form.type === "true_false";

  const canSubmit = useMemo(() => {
    if (!form.subject_id || !form.question_text.trim()) return false;
    if (form.type === "mcq") {
      const validOptions = form.options.filter(
        (o) => o.option_text.trim() !== ""
      );
      if (validOptions.length < 2) return false;
      const anyCorrect = validOptions.some((o) => o.is_correct);
      return anyCorrect;
    }
    if (form.type === "true_false") {
      return (
        form.options.length === 2 && form.options.some((o) => o.is_correct)
      );
    }
    return true;
  }, [form]);

  const updateOption = (idx, key, value) => {
    setForm((prev) => ({
      ...prev,
      options: prev.options.map((o, i) =>
        i === idx ? { ...o, [key]: value } : o
      ),
    }));
  };

  const addOption = () =>
    setForm((prev) => ({
      ...prev,
      options: [...prev.options, defaultOption()],
    }));
  const removeOption = (idx) =>
    setForm((prev) => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== idx),
    }));

  const onSubmit = async (e) => {
    e.preventDefault();
    if (!canSubmit) return;
    setSaving(true);
    setError("");
    try {
      const payload = {
        subject_id: Number(form.subject_id),
        question_text: form.question_text.trim(),
        direction: form.direction,
        type: form.type,
        options: form.options
          .filter((o) => o.option_text.trim() !== "")
          .map((o) => ({
            option_text: o.option_text.trim(),
            direction: o.direction,
            is_correct: !!o.is_correct,
          })),
      };
      await api.put(`/admin/questions/${id}`, payload);
      navigate("/admin/questions");
    } catch (e) {
      console.error(e);
      setError(e.response?.data?.message || "Failed to update question");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
    );
  }

  return (
    <div className="edit-question-container">
      <div className="header-row">
        <div className="header-left">
          <button
            onClick={() => navigate("/admin/questions")}
            className="back-button"
          >
            <ArrowLeft className="back-icon" />
            Back
          </button>
          <h1 className="page-title">Edit Question</h1>
        </div>
      </div>

      {error && <div className="error-message">{error}</div>}

      <form onSubmit={onSubmit} className="edit-form">
        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Subject</label>
            <select
              value={form.subject_id}
              onChange={(e) =>
                setForm((p) => ({ ...p, subject_id: e.target.value }))
              }
              className="form-select"
            >
              <option value="">Select subject</option>
              {subjects.map((s) => (
                <option key={s.id} value={s.id}>
                  {s.name}
                </option>
              ))}
            </select>
          </div>
          <div className="form-group">
            <label className="form-label">Direction</label>
            <select
              value={form.direction}
              onChange={(e) =>
                setForm((p) => ({ ...p, direction: e.target.value }))
              }
              className="form-select"
            >
              <option value="ltr">Left to Right</option>
              <option value="rtl">Right to Left</option>
            </select>
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">Type</label>
          <div className="radio-group">
            <label className="radio-option">
              <input
                type="radio"
                name="type"
                value="mcq"
                checked={form.type === "mcq"}
                onChange={() => setForm((p) => ({ ...p, type: "mcq" }))}
              />
              <span>Multiple Choice</span>
            </label>
            <label className="radio-option">
              <input
                type="radio"
                name="type"
                value="true_false"
                checked={form.type === "true_false"}
                onChange={() => setForm((p) => ({ ...p, type: "true_false" }))}
              />
              <span>True / False</span>
            </label>
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">Question Text</label>
          <textarea
            rows={3}
            value={form.question_text}
            onChange={(e) =>
              setForm((p) => ({ ...p, question_text: e.target.value }))
            }
            className="form-textarea"
            placeholder="Enter the question text"
          />
        </div>

        <div className="form-group">
          <div className="options-header">
            <label className="form-label">Options</label>
            {form.type === "mcq" && (
              <button
                type="button"
                onClick={addOption}
                className="add-option-button"
              >
                <Plus className="add-icon" /> Add Option
              </button>
            )}
          </div>
          <div className="options-list">
            {form.options.map((opt, idx) => (
              <div key={idx} className="option-row">
                <input
                  type="checkbox"
                  checked={!!opt.is_correct}
                  onChange={(e) =>
                    updateOption(idx, "is_correct", e.target.checked)
                  }
                  className="correct-checkbox"
                  title="Mark as correct"
                />
                <div className="option-fields">
                  <input
                    type="text"
                    value={opt.option_text}
                    onChange={(e) =>
                      updateOption(idx, "option_text", e.target.value)
                    }
                    className="option-input"
                    placeholder={`Option ${idx + 1}`}
                    disabled={isTrueFalse}
                  />
                  <select
                    value={opt.direction}
                    onChange={(e) =>
                      updateOption(idx, "direction", e.target.value)
                    }
                    className="option-select"
                    disabled={isTrueFalse}
                  >
                    <option value="ltr">LTR</option>
                    <option value="rtl">RTL</option>
                  </select>
                  {form.type === "mcq" && (
                    <button
                      type="button"
                      onClick={() => removeOption(idx)}
                      className="remove-option-button"
                      title="Remove option"
                    >
                      <Trash2 className="remove-icon" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="form-actions">
          <button
            type="button"
            onClick={() => navigate("/admin/questions")}
            className="cancel-button"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!canSubmit || saving}
            className="submit-button"
          >
            {saving ? "Saving..." : "Update Question"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default QuestionEdit;
