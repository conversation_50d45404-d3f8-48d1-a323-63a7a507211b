import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import api from "../../services/api";
import { ArrowLeft, Plus, Edit, Trash2 } from "lucide-react";
import "../../styles/subject-details.css";
import { HashLoader } from "react-spinners";

const SubjectDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [subject, setSubject] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchSubjectDetails();
  }, [id]);

  const fetchSubjectDetails = async () => {
    try {
      const response = await api.get(`/admin/subjects/${id}`);
      setSubject(response.data);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching subject details:", error);
      setError("Failed to load subject details");
      setIsLoading(false);
    }
  };

  const handleDeleteQuestion = async (questionId) => {
    if (window.confirm("Are you sure you want to delete this question?")) {
      try {
        await api.delete(`/admin/questions/${questionId}`);
        fetchSubjectDetails();
      } catch (error) {
        console.error("Error deleting question:", error);
        setError("Failed to delete question");
      }
    }
  };

  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
    );
  }

  if (error || !subject) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-red-500 text-center">
          <p className="text-xl font-semibold mb-4">Error</p>
          <p>{error || "Subject not found"}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="subject-details-page">
      <div className="subject-header">
        <div className="subject-info-group">
          <button
            onClick={() => navigate("/admin/subjects")}
            className="btn btn-back"
          >
            <ArrowLeft className="btn-icon" />
            Back to Subjects
          </button>
          <div className="subject-info">
            <h1 className="subject-title">{subject.name}</h1>
            <p className="subject-description">{subject.description}</p>
          </div>
        </div>
        <button
          onClick={() => navigate(`/admin/subjects/${id}/questions/create`)}
          className="btn btn-primary"
        >
          <Plus className="btn-icon" />
          Add Question
        </button>
      </div>

      <div className="subject-stats">
        <div className="stat-card">
          <h3 className="stat-label">Total Questions</h3>
          <p className="stat-value stat-blue">
            {subject.questions?.length || 0}
          </p>
        </div>
        <div className="stat-card">
          <h3 className="stat-label">Total Exams</h3>
          <p className="stat-value stat-green">{subject.exams?.length || 0}</p>
        </div>
        <div className="stat-card">
          <h3 className="stat-label">Created</h3>
          <p className="stat-date">
            {new Date(subject.created_at).toLocaleDateString()}
          </p>
        </div>
      </div>

      <div className="questions-section">
        <div className="questions-header">
          <h2 className="questions-title">Questions</h2>
        </div>
        <div className="questions-list">
          {subject.questions?.length > 0 ? (
            subject.questions.map((question, index) => (
              <div key={question.id} className="question-card">
                <div className="question-top">
                  <div className="question-content">
                    <h3 className="question-title">Question {index + 1}</h3>
                    <p className="question-text">{question.question_text}</p>
                    {question.direction && (
                      <p className="question-direction">{question.direction}</p>
                    )}
                    <div className="options-list">
                      {question.options?.map((option, optIndex) => (
                        <div
                          key={option.id}
                          className={`option-item ${
                            option.is_correct
                              ? "option-correct"
                              : "option-default"
                          }`}
                        >
                          <span
                            className={`option-text ${
                              option.is_correct
                                ? "text-correct"
                                : "text-default"
                            }`}
                          >
                            {String.fromCharCode(65 + optIndex)}.{" "}
                            {option.option_text}
                          </span>
                          {option.direction && (
                            <span className="option-direction">
                              ({option.direction})
                            </span>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="question-actions">
                    <button
                      onClick={() =>
                        navigate(`/admin/questions/${question.id}/edit`)
                      }
                      className="btn-icon btn-edit"
                      title="Edit Question"
                    >
                      <Edit className="btn-icon" />
                    </button>
                    <button
                      onClick={() => handleDeleteQuestion(question.id)}
                      className="btn-icon btn-delete"
                      title="Delete Question"
                    >
                      <Trash2 className="btn-icon" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="no-questions">
              No questions found for this subject.
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SubjectDetails;
