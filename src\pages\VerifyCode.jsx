import React, { useRef, useState } from "react";
import "../styles/verify-code.css";
import Logo from "../assets/logo.png";
import { verifyCode } from "../store/slices/authSlice";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";
import { HashLoader } from "react-spinners";
import { useNavigate } from "react-router-dom";

const VerifyCode = () => {
  const inputsRef = useRef([]);
  const [code, setCode] = useState("");
  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state) => state.auth);
  const navigate = useNavigate();

  // const
  const handleInputChange = (e, index) => {
    const value = e.target.value;
    if (/^\d$/.test(value)) {
      e.target.value = value;
      updateCode();
      if (index < 5) {
        inputsRef.current[index + 1].focus();
      }
    } else {
      e.target.value = "";
    }
  };

  const handleKeyDown = (e, index) => {
    if (e.key === "Backspace" && !e.target.value && index > 0) {
      inputsRef.current[index - 1].focus();
    }
  };

  const updateCode = () => {
    const currentCode = inputsRef.current.map((input) => input.value).join("");
    setCode(currentCode);
  };

  const handleVerify = async () => {
    try {
      await dispatch(verifyCode({ email: user?.email, code: code })).unwrap();
      navigate("/student");
    } catch (error) {
      toast.error(error || "Something went wrong!");
    }
  };
  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto ",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
          borderRadius: "50%",
          backgroundColor: "transparent",
        }}
      />
    );
  }
  return (
    <div className="verify-code-page">
      <div className="verify-container">
        <div className="image">
          <img src={Logo} alt="logo" />
        </div>
        <h2 className="verify-code-page__title">Enter Verification Code</h2>
        <div className="verify-code-page__inputs">
          {[...Array(6)].map((_, i) => (
            <input
              key={i}
              type="text"
              inputMode="numeric"
              maxLength="1"
              className="verify-code-page__input"
              onChange={(e) => handleInputChange(e, i)}
              onKeyDown={(e) => handleKeyDown(e, i)}
              ref={(el) => (inputsRef.current[i] = el)}
            />
          ))}
        </div>
        <button
          className="verify-code-page__send-button"
          onClick={handleVerify}
        >
          Send Code
        </button>
      </div>
    </div>
  );
};

export default VerifyCode;
