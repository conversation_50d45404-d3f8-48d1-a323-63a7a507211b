/* Student Home Component Styles */
.student-hero {
  min-height: 80vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

.student-hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.student-hero .title {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  z-index: 2;
  position: relative;
}

.student-hero .title svg {
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
  color: #1c2a45;
  animation: float 3s ease-in-out infinite;
}
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.student-hero .title h1 {
  font-size: 3rem;
  font-weight: 800;
  margin: 0;
  letter-spacing: -0.02em;
  color: #1c2a45;
}

.student-hero .image {
  margin: 0rem auto 14px;
  z-index: 2;
  position: relative;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  width: 500px;
  max-width: 90%;
}

.student-hero .image:hover {
  transform: translateY(-10px);
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.3);
}

.student-hero .image img {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 10px;
}

.student-hero p {
  font-size: 1.25rem;
  color: #1c2a45;
  text-align: center;
  max-width: 600px;
  line-height: 1.6;
  margin: 2rem 0;
  z-index: 2;
  position: relative;
  text-shadow: 0 2px 10px rgb(8 13 20 / 51%);
  font-weight: 300;
}

.student-hero .nav-container {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
  z-index: 2;
  position: relative;
}

.student-hero .nav-container .link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: rgb(10 15 25);
  border: 2px solid rgb(8 192 175);
  border-radius: 50%;
  color: #ffffff;
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.student-hero .nav-container .link::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.student-hero .nav-container .link:hover::before {
  opacity: 1;
}

.student-hero .nav-container .link:hover {
  transform: translateY(-6px) scale(1.1);
  background-color: #297c81;
  border-color: rgba(255, 255, 255, 0.4);
  box-shadow: 0px 0px 5px rgba(0, 0, 0, 0.3);
}

.student-hero .nav-container .link:active {
  transform: translateY(-4px) scale(1.05);
}

.student-hero .nav-container .link svg {
  z-index: 1;
  position: relative;
  transition: transform 0.3s ease;
}

.student-hero .nav-container .link:hover svg {
  transform: scale(1.2);
}

/* Responsive Design */
@media (max-width: 768px) {
  .student-hero {
    padding: 1rem;
    text-align: center;
  }

  .student-hero .title h1 {
    font-size: 2.5rem;
  }

  .student-hero .title {
    flex-direction: column;
    gap: 0.5rem;
  }

  .student-hero p {
    font-size: 1.1rem;
    padding: 0 1rem;
  }

  .student-hero .nav-container {
    gap: 1.5rem;
    flex-wrap: wrap;
    justify-content: center;
  }

  .student-hero .nav-container .link {
    width: 70px;
    height: 70px;
  }
}

@media (max-width: 480px) {
  .student-hero {
    padding: 0.5rem;
  }

  .student-hero .title h1 {
    font-size: 2rem;
  }

  .student-hero p {
    font-size: 1rem;
  }

  .student-hero .nav-container {
    gap: 1rem;
  }

  .student-hero .nav-container .link {
    width: 60px;
    height: 60px;
  }

  .student-hero .nav-container .link svg {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 320px) {
  .student-hero .title h1 {
    font-size: 1.75rem;
  }

  .student-hero .nav-container {
    flex-direction: column;
    align-items: center;
  }
}

/* Animation for page load */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.student-hero .title {
  animation: fadeInUp 0.8s ease-out;
}

.student-hero .image {
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.student-hero p {
  animation: fadeInUp 0.8s ease-out 0.4s both;
}

.student-hero .nav-container {
  animation: fadeInUp 0.8s ease-out 0.6s both;
}

/* Accessibility improvements */
.student-hero .nav-container .link:focus {
  outline: 3px solid rgba(255, 255, 255, 0.6);
  outline-offset: 4px;
}

/* Dashboard Stats */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 2rem 0;
  width: 100%;
  max-width: 800px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.stat-info h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: #1c2a45;
}

.stat-info p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
  font-weight: 500;
}

/* Dashboard Content */
.dashboard-content {
  width: 100%;
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 2rem;
}

.dashboard-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.dashboard-section h2 {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin: 0 0 1.5rem 0;
  color: #1c2a45;
  font-size: 1.5rem;
  font-weight: 600;
}

/* Exam Lists */
.exam-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.exam-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #1c2a45;
  transition: transform 0.2s ease;
}

.exam-item:hover {
  transform: translateX(4px);
}

.exam-item.upcoming {
  border-left-color: #ff6b35;
}

.exam-item h4 {
  margin: 0 0 0.5rem 0;
  color: #1c2a45;
  font-weight: 600;
}

.exam-item p {
  margin: 0 0 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.exam-duration, .exam-start {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.8rem;
  color: #888;
  background: rgba(28, 42, 69, 0.1);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* Attempts List */
.attempts-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.attempt-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #4CAF50;
}

.attempt-info h4 {
  margin: 0 0 0.25rem 0;
  color: #1c2a45;
  font-weight: 600;
}

.attempt-info p {
  margin: 0 0 0.25rem 0;
  color: #666;
  font-size: 0.9rem;
}

.attempt-date {
  font-size: 0.8rem;
  color: #888;
}

.attempt-score .score {
  font-weight: 700;
  color: #4CAF50;
  font-size: 1.1rem;
}

.attempt-score .in-progress {
  color: #ff9800;
  font-weight: 600;
  font-size: 0.9rem;
}

/* View All Link */
.view-all-link {
  display: inline-flex;
  align-items: center;
  color: #1c2a45;
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.view-all-link:hover {
  color: #ff6b35;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .stat-info h3 {
    font-size: 1.5rem;
  }

  .dashboard-content {
    padding: 0 1rem;
  }

  .dashboard-section {
    padding: 1.5rem;
  }

  .exam-list {
    grid-template-columns: 1fr;
  }

  .attempt-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: 0.75rem;
  }

  .dashboard-section {
    padding: 1rem;
  }
}

/* @media (prefers-reduced-motion: reduce) {
  .student-hero .title svg,
  .student-hero .image,
  .student-hero .nav-container .link {
    animation: none;
    transition: none;
  }
} */
