import { useState } from "react";
import { useNavigate, <PERSON> } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useDispatch } from "react-redux";
import { registerUser } from "../store/slices/authSlice";
import "../styles/authForms.css";
import { toast } from "react-toastify";
import Logo from "../assets/logo.png";
import { HashLoader } from "react-spinners";
import { useSelector } from "react-redux";

const registerSchema = z
  .object({
    name: z.string().min(1, "Name is required"),
    email: z.string().email("Invalid email address"),
    password: z.string().min(8, "Password must be at least 8 characters"),
    password_confirmation: z
      .string()
      .min(8, "Password confirmation is required"),
  })
  .refine((data) => data.password === data.password_confirmation, {
    message: "Passwords don't match",
    path: ["password_confirmation"],
  });

const StudentRegister = () => {
  // const [isLoading, setIsLoading] = useState(false);
  // const [error, setError] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const { isLoading } = useSelector((state) => state.auth);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(registerSchema),
  });

  const onSubmit = async (data) => {
    try {
      const result = await dispatch(
        registerUser({ data, type: "student" })
      ).unwrap();
      navigate("/code-verify");
    } catch (error) {
      toast.error(error || "Something went wrong!");
    }
  };
  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto ",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
          borderRadius: "50%",
          backgroundColor: "transparent",
        }}
      />
    );
  }
  return (
    <div className="auth-page">
      <div className="form-card">
        <div className="image">
          <img src={Logo} alt="login-logo" />
        </div>
        <div>
          <h2 className="form-title">Student Registration</h2>
          <p className="form-subtitle">Create your student account</p>
        </div>
        <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="form-group">
            <label htmlFor="name" className="form-label">
              Full Name
            </label>
            <input
              {...register("name")}
              id="name"
              name="name"
              type="text"
              autoComplete="name"
              required
              className="form-control"
              placeholder="Full Name"
            />
            {errors.name && <p className="form-error">{errors.name.message}</p>}
          </div>
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email address
            </label>
            <input
              {...register("email")}
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              className="form-control"
              placeholder="Email address"
            />
            {errors.email && (
              <p className="form-error">{errors.email.message}</p>
            )}
          </div>
          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Password
            </label>
            <input
              {...register("password")}
              id="password"
              name="password"
              type="password"
              autoComplete="new-password"
              required
              className="form-control"
              placeholder="Password"
            />
            {/* {errors.password && (
              <p className="form-error">{errors.password.message}</p>
            )} */}
          </div>
          <div className="form-group">
            <label htmlFor="password_confirmation" className="form-label">
              Confirm Password
            </label>
            <input
              {...register("password_confirmation")}
              id="password_confirmation"
              name="password_confirmation"
              type="password"
              autoComplete="new-password"
              required
              className="form-control"
              placeholder="Confirm Password"
            />
            {errors.password_confirmation && (
              <p className="form-error">
                {errors.password_confirmation.message}
              </p>
            )}
          </div>

          {/* {error && <div className="form-error text-center">{error}</div>} */}

          <div className="form-actions">
            <button
              type="submit"
              disabled={isLoading}
              className="btn btn-block"
            >
              {isLoading ? "Creating account..." : "Create Account"}
            </button>
          </div>

          <div style={{ textAlign: "center" }}>
            <Link to="/login">Already have an account? Sign in</Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StudentRegister;
