/*!
 * Font Awesome Free 7.0.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2025 Fonticons, Inc.
 */
(t=>{("object"!=typeof exports||"undefined"==typeof module)&&"function"==typeof define&&define.amd?define(t):t()})(function(){function n(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,n=Array(e);o<e;o++)n[o]=t[o];return n}function r(t,e,o){return(e="symbol"==typeof(n=((t,e)=>{if("object"!=typeof t||!t)return t;var o=t[Symbol.toPrimitive];if(void 0===o)return("string"===e?String:Number)(t);if("object"!=typeof(o=o.call(t,e||"default")))return o;throw new TypeError("@@toPrimitive must return a primitive value.")})(e,"string"))?n:n+"")in t?Object.defineProperty(t,e,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[e]=o,t;var n}function a(e,t){var o,n=Object.keys(e);return Object.getOwnPropertySymbols&&(o=Object.getOwnPropertySymbols(e),t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)),n}function l(e){for(var t=1;t<arguments.length;t++){var o=null!=arguments[t]?arguments[t]:{};t%2?a(Object(o),!0).forEach(function(t){r(e,t,o[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):a(Object(o)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))})}return e}function t(t){return(t=>{if(Array.isArray(t))return n(t)})(t)||(t=>{if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)})(t)||((t,e)=>{var o;if(t)return"string"==typeof t?n(t,e):"Map"===(o="Object"===(o={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:o)||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?n(t,e):void 0})(t)||(()=>{throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")})()}function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var o={},i={};try{"undefined"!=typeof window&&(o=window),"undefined"!=typeof document&&(i=document)}catch(t){}function f(){d.removeEventListener("DOMContentLoaded",f),p=1,g.map(function(t){return t()})}var s=(o.navigator||{}).userAgent,s=void 0===s?"":s,c=o,d=i,u=!!c.document,h=!!d.documentElement&&!!d.head&&"function"==typeof d.addEventListener&&"function"==typeof d.createElement,g=(~s.indexOf("MSIE")||s.indexOf("Trident/"),[]),p=!1;function m(t){h&&(p?setTimeout(t,0):g.push(t))}h&&!(p=(d.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(d.readyState))&&d.addEventListener("DOMContentLoaded",f);var W="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};(function(t){function d(t,e){var o=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(o>>16)<<16|65535&o}function l(t,e,o,n,r,a){return d((e=d(d(e,t),d(n,a)))<<r|e>>>32-r,o)}function u(t,e,o,n,r,a,i){return l(e&o|~e&n,t,e,r,a,i)}function h(t,e,o,n,r,a,i){return l(e&n|o&~n,t,e,r,a,i)}function g(t,e,o,n,r,a,i){return l(e^o^n,t,e,r,a,i)}function p(t,e,o,n,r,a,i){return l(o^(e|~n),t,e,r,a,i)}function f(t,e){t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;for(var o,n,r,a,i=1732584193,l=-271733879,f=-1732584194,s=271733878,c=0;c<t.length;c+=16)i=u(o=i,n=l,r=f,a=s,t[c],7,-680876936),s=u(s,i,l,f,t[c+1],12,-389564586),f=u(f,s,i,l,t[c+2],17,606105819),l=u(l,f,s,i,t[c+3],22,-1044525330),i=u(i,l,f,s,t[c+4],7,-176418897),s=u(s,i,l,f,t[c+5],12,1200080426),f=u(f,s,i,l,t[c+6],17,-1473231341),l=u(l,f,s,i,t[c+7],22,-45705983),i=u(i,l,f,s,t[c+8],7,1770035416),s=u(s,i,l,f,t[c+9],12,-1958414417),f=u(f,s,i,l,t[c+10],17,-42063),l=u(l,f,s,i,t[c+11],22,-1990404162),i=u(i,l,f,s,t[c+12],7,1804603682),s=u(s,i,l,f,t[c+13],12,-40341101),f=u(f,s,i,l,t[c+14],17,-1502002290),i=h(i,l=u(l,f,s,i,t[c+15],22,1236535329),f,s,t[c+1],5,-165796510),s=h(s,i,l,f,t[c+6],9,-1069501632),f=h(f,s,i,l,t[c+11],14,643717713),l=h(l,f,s,i,t[c],20,-373897302),i=h(i,l,f,s,t[c+5],5,-701558691),s=h(s,i,l,f,t[c+10],9,38016083),f=h(f,s,i,l,t[c+15],14,-660478335),l=h(l,f,s,i,t[c+4],20,-405537848),i=h(i,l,f,s,t[c+9],5,568446438),s=h(s,i,l,f,t[c+14],9,-1019803690),f=h(f,s,i,l,t[c+3],14,-187363961),l=h(l,f,s,i,t[c+8],20,1163531501),i=h(i,l,f,s,t[c+13],5,-1444681467),s=h(s,i,l,f,t[c+2],9,-51403784),f=h(f,s,i,l,t[c+7],14,1735328473),i=g(i,l=h(l,f,s,i,t[c+12],20,-1926607734),f,s,t[c+5],4,-378558),s=g(s,i,l,f,t[c+8],11,-2022574463),f=g(f,s,i,l,t[c+11],16,1839030562),l=g(l,f,s,i,t[c+14],23,-35309556),i=g(i,l,f,s,t[c+1],4,-1530992060),s=g(s,i,l,f,t[c+4],11,1272893353),f=g(f,s,i,l,t[c+7],16,-155497632),l=g(l,f,s,i,t[c+10],23,-1094730640),i=g(i,l,f,s,t[c+13],4,681279174),s=g(s,i,l,f,t[c],11,-358537222),f=g(f,s,i,l,t[c+3],16,-722521979),l=g(l,f,s,i,t[c+6],23,76029189),i=g(i,l,f,s,t[c+9],4,-640364487),s=g(s,i,l,f,t[c+12],11,-421815835),f=g(f,s,i,l,t[c+15],16,530742520),i=p(i,l=g(l,f,s,i,t[c+2],23,-995338651),f,s,t[c],6,-198630844),s=p(s,i,l,f,t[c+7],10,1126891415),f=p(f,s,i,l,t[c+14],15,-1416354905),l=p(l,f,s,i,t[c+5],21,-57434055),i=p(i,l,f,s,t[c+12],6,1700485571),s=p(s,i,l,f,t[c+3],10,-1894986606),f=p(f,s,i,l,t[c+10],15,-1051523),l=p(l,f,s,i,t[c+1],21,-2054922799),i=p(i,l,f,s,t[c+8],6,1873313359),s=p(s,i,l,f,t[c+15],10,-30611744),f=p(f,s,i,l,t[c+6],15,-1560198380),l=p(l,f,s,i,t[c+13],21,1309151649),i=p(i,l,f,s,t[c+4],6,-145523070),s=p(s,i,l,f,t[c+11],10,-1120210379),f=p(f,s,i,l,t[c+2],15,718787259),l=p(l,f,s,i,t[c+9],21,-343485551),i=d(i,o),l=d(l,n),f=d(f,r),s=d(s,a);return[i,l,f,s]}function s(t){for(var e="",o=32*t.length,n=0;n<o;n+=8)e+=String.fromCharCode(t[n>>5]>>>n%32&255);return e}function c(t){var e=[];for(e[(t.length>>2)-1]=void 0,n=0;n<e.length;n+=1)e[n]=0;for(var o=8*t.length,n=0;n<o;n+=8)e[n>>5]|=(255&t.charCodeAt(n/8))<<n%32;return e}function n(t){for(var e,o="0123456789abcdef",n="",r=0;r<t.length;r+=1)e=t.charCodeAt(r),n+=o.charAt(e>>>4&15)+o.charAt(15&e);return n}function m(t){return unescape(encodeURIComponent(t))}function r(t){return s(f(c(t=m(t)),8*t.length))}function a(t,e){var o,n,t=m(t),e=m(e),r=c(t),a=[],i=[];for(a[15]=i[15]=void 0,16<r.length&&(r=f(r,8*t.length)),o=0;o<16;o+=1)a[o]=909522486^r[o],i[o]=1549556828^r[o];return n=f(a.concat(c(e)),512+8*e.length),s(f(i.concat(n),640))}function e(t,e,o){return e?o?a(e,t):n(a(e,t)):o?r(t):n(r(t))}var o;o=W,t.exports?t.exports=e:o.md5=e})(b={exports:{}});var b,y=b.exports;function w(t){if(null!==t&&"object"===e(t))return t.src?y(t.src):t.href?y(t.href):t.innerText&&""!==t.innerText?y(t.innerText):void 0}var v="fa-kits-diag",j="fa-kits-node-under-test",x="data-md5",A="data-fa-detection-ignore",T="data-fa-detection-timeout",D="data-fa-detection-results-collection-max-wait",k=function(t){t.preventDefault(),t.stopPropagation()};function E(t){var e=t.fn,a=void 0===e?function(){return!0}:e,e=t.initialDuration,o=void 0===e?1:e,e=t.maxDuration,i=void 0===e?c.FontAwesomeDetection.timeout:e,e=t.showProgress,l=void 0!==e&&e,f=t.progressIndicator;return new Promise(function(n,r){!function e(t,o){setTimeout(function(){var t=a();l&&console.info(f),t?n(t):(t=250+o)<=i?e(250,t):r("timeout")},t)}(o,0)})}function B(e){for(var a=Array.from(d.scripts).filter(function(t){return!t.hasAttribute(A)&&t!==e}),i={},l=0;l<a.length;l++)(()=>{var t=d.createElement("iframe"),e=(t.setAttribute("style","display:none;"),d.createElement("script")),o=(e.setAttribute("id",j),w(a[l])),n=(e.setAttribute(x,o),i[o]=a[l],""!==a[l].src&&(e.src=a[l].src),""!==a[l].innerText&&(e.innerText=a[l].innerText),e.async=!0,d.createElement("script")),r=(n.setAttribute("id",v),"file://"===c.location.origin?"*":c.location.origin);n.innerText="(".concat((function(o,n,r){parent.FontAwesomeDetection.__pollUntil({fn:function(){return!!window.FontAwesomeConfig||!!window.FontAwesomeKitConfig}}).then(function(){var t=document.getElementById(o);parent.postMessage({type:"fontawesome-conflict",technology:"js",src:t.src,innerText:t.innerText,tagName:t.tagName,md5:n},r)}).catch(function(t){var e=document.getElementById(o);"timeout"===t?parent.postMessage({type:"no-conflict",src:e.src,innerText:e.innerText,tagName:e.tagName,md5:n},r):console.error(t)})}).toString(),")('").concat(j,"', '").concat(o,"', '").concat(r,"');"),t.onload=function(){t.contentWindow.addEventListener("error",k,!0),t.contentDocument.head.appendChild(n),t.contentDocument.head.appendChild(e)},m(function(){return d.body.appendChild(t)})})();return i}function C(t){var e=t.nodesTested,o=t.nodesFound;c.FontAwesomeDetection=c.FontAwesomeDetection||{},c.FontAwesomeDetection.nodesTested=e,c.FontAwesomeDetection.nodesFound=o,c.FontAwesomeDetection.detectionDone=!0}function L(t){var e=0<arguments.length&&void 0!==t?t:function(){},o={conflict:{},noConflict:{}},n=(c.onmessage=function(t){"file://"!==c.location.origin&&t.origin!==c.location.origin||t&&t.data&&("fontawesome-conflict"===t.data.type?o.conflict[t.data.md5]=t.data:"no-conflict"===t.data.type&&(o.noConflict[t.data.md5]=t.data))},B(d.currentScript)),r=(()=>{var t=Array.from(d.getElementsByTagName("link")).filter(function(t){return!t.hasAttribute(A)}),e=Array.from(d.getElementsByTagName("style")).filter(function(t){return!(t.hasAttribute(A)||c.FontAwesomeConfig&&t.innerText.match(new RegExp("svg:not\\(:root\\)\\.".concat(c.FontAwesomeConfig.replacementClass))))});function o(t,e){var o=d.createElement("iframe"),n=(o.setAttribute("style","visibility: hidden; position: absolute; height: 0; width: 0;"),"fa-test-icon-"+e),r=d.createElement("i"),a=(r.setAttribute("class","fa fa-coffee"),r.setAttribute("id",n),d.createElement("script")),i=(a.setAttribute("id",v),"file://"===c.location.origin?"*":c.location.origin);a.innerText="(".concat((function(o,e,n,r){parent.FontAwesomeDetection.__pollUntil({fn:function(){var t=document.getElementById(e),t=window.getComputedStyle(t).getPropertyValue("font-family");return!(!t.match(/FontAwesome/)&&!t.match(/Font Awesome [56]/))}}).then(function(){var t=document.getElementById(o);parent.postMessage({type:"fontawesome-conflict",technology:"webfont",href:t.href,innerText:t.innerText,tagName:t.tagName,md5:n},r)}).catch(function(t){var e=document.getElementById(o);"timeout"===t?parent.postMessage({type:"no-conflict",technology:"webfont",href:e.src,innerText:e.innerText,tagName:e.tagName,md5:n},r):console.error(t)})}).toString(),")('").concat(j,"', '").concat(n||"foo","', '").concat(e,"', '").concat(i,"');"),o.onload=function(){o.contentWindow.addEventListener("error",k,!0),o.contentDocument.head.appendChild(a),o.contentDocument.head.appendChild(t),o.contentDocument.body.appendChild(r)},m(function(){return d.body.appendChild(o)})}for(var n={},r=0;r<t.length;r++){var a=d.createElement("link"),i=(a.setAttribute("id",j),a.setAttribute("href",t[r].href),a.setAttribute("rel",t[r].rel),w(t[r]));a.setAttribute(x,i),n[i]=t[r],o(a,i)}for(var l=0;l<e.length;l++){var f=d.createElement("style"),s=(f.setAttribute("id",j),w(e[l]));f.setAttribute(x,s),f.innerText=e[l].innerText,n[s]=e[l],o(f,s)}return n})(),a=l(l({},n),r),i=Object.keys(n).length+Object.keys(r).length,n=c.FontAwesomeDetection.timeout+c.FontAwesomeDetection.resultsCollectionMaxWait;console.group("Font Awesome Detector"),0===i?(console.info("%cAll Good!","color: green; font-size: large"),console.info("We didn't find anything that needs testing for conflicts. Ergo, no conflicts.")):(console.info("Testing ".concat(i," possible conflicts.")),console.info("We'll wait about ".concat(Math.round(c.FontAwesomeDetection.timeout/10)/100," seconds while testing these and\n")+"then up to another ".concat(Math.round(c.FontAwesomeDetection.resultsCollectionMaxWait/10)/100," to allow the browser time\n")+"to accumulate the results. But we'll probably be outta here way before then.\n\n"),console.info("You can adjust those durations by assigning values to these attributes on the <script> element that loads this detection:"),console.info("\t%c".concat(T,"%c: milliseconds to wait for each test before deciding whether it's a conflict."),"font-weight: bold;","font-size: normal;"),console.info("\t%c".concat(D,"%c: milliseconds to wait for the browser to accumulate test results before giving up."),"font-weight: bold;","font-size: normal;"),E({maxDuration:n,showProgress:!0,progressIndicator:"waiting...",fn:function(){return Object.keys(o.conflict).length+Object.keys(o.noConflict).length>=i}}).then(function(){console.info("DONE!"),C({nodesTested:o,nodesFound:a}),e({nodesTested:o,nodesFound:a}),console.groupEnd()}).catch(function(t){"timeout"===t?console.info("TIME OUT! We waited until we got tired. Here's what we found:"):(console.info("Whoops! We hit an error:",t),console.info("Here's what we'd found up until that error:")),C({nodesTested:o,nodesFound:a}),e({nodesTested:o,nodesFound:a}),console.groupEnd()}))}var o=c.FontAwesomeDetection||{},i=l(l(l({},{report:function(t){var e,o=t.nodesTested,n=t.nodesFound,r={};for(e in n)o.conflict[e]||o.noConflict[e]||(r[e]=n[e]);var a=Object.keys(o.conflict).length;if(0<a){console.info("%cConflict".concat(1<a?"s":""," found:"),"color: darkred; font-size: large");var i,l={};for(i in o.conflict){var f=o.conflict[i];l[i]={tagName:f.tagName,"src/href":f.src||f.href||"n/a","innerText excerpt":f.innerText&&""!==f.innerText?f.innerText.slice(0,200)+"...":"(empty)"}}console.table(l)}if(0<(a=Object.keys(o.noConflict).length)){console.info("%cNo conflict".concat(1<a?"s":""," found with ").concat(1===a?"this":"these",":"),"color: green; font-size: large");var s,c={};for(s in o.noConflict){var d=o.noConflict[s];c[s]={tagName:d.tagName,"src/href":d.src||d.href||"n/a","innerText excerpt":d.innerText&&""!==d.innerText?d.innerText.slice(0,200)+"...":"(empty)"}}console.table(c)}if(0<(a=Object.keys(r).length)){console.info("%cLeftovers--we timed out before collecting test results for ".concat(1===a?"this":"these",":"),"color: blue; font-size: large");var u,h={};for(u in r){var g=r[u];h[u]={tagName:g.tagName,"src/href":g.src||g.href||"n/a","innerText excerpt":g.innerText&&""!==g.innerText?g.innerText.slice(0,200)+"...":"(empty)"}}console.table(h)}},timeout:+(d.currentScript.getAttribute(T)||"2000"),resultsCollectionMaxWait:+(d.currentScript.getAttribute(D)||"5000")}),o),{},{__pollUntil:E,md5ForNode:w,detectionDone:!1,nodesTested:null,nodesFound:null}),s=(c.FontAwesomeDetection=i,{classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"},slab:{"fa-regular":"regular",faslr:"regular"},"slab-press":{"fa-regular":"regular",faslpr:"regular"},thumbprint:{"fa-light":"light",fatl:"light"},whiteboard:{"fa-semibold":"semibold",fawsb:"semibold"},notdog:{"fa-solid":"solid",fans:"solid"},"notdog-duo":{"fa-solid":"solid",fands:"solid"},etch:{"fa-solid":"solid",faes:"solid"},jelly:{"fa-regular":"regular",fajr:"regular"},"jelly-fill":{"fa-regular":"regular",fajfr:"regular"},"jelly-duo":{"fa-regular":"regular",fajdr:"regular"},chisel:{"fa-regular":"regular",facr:"regular"}}),O="classic",i=(r(r(r(r(r(r(r(r(r(r(o={},O,"Classic"),"duotone","Duotone"),"sharp","Sharp"),"sharp-duotone","Sharp Duotone"),"chisel","Chisel"),"etch","Etch"),"jelly","Jelly"),"jelly-duo","Jelly Duo"),"jelly-fill","Jelly Fill"),"notdog","Notdog"),r(r(r(r(r(o,"notdog-duo","Notdog Duo"),"slab","Slab"),"slab-press","Slab Press"),"thumbprint","Thumbprint"),"whiteboard","Whiteboard"),{fak:"kit","fa-kit":"kit"}),o={fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"},z=(r(r({},"kit","Kit"),"kit-duotone","Kit Duotone"),{kit:"fak"}),_={"kit-duotone":"fakd"},F="duotone-group",S="swap-opacity",N="primary",P="secondary",M=(r(r(r(r(r(r(r(r(r(r(M={},"classic","Classic"),"duotone","Duotone"),"sharp","Sharp"),"sharp-duotone","Sharp Duotone"),"chisel","Chisel"),"etch","Etch"),"jelly","Jelly"),"jelly-duo","Jelly Duo"),"jelly-fill","Jelly Fill"),"notdog","Notdog"),r(r(r(r(r(M,"notdog-duo","Notdog Duo"),"slab","Slab"),"slab-press","Slab Press"),"thumbprint","Thumbprint"),"whiteboard","Whiteboard"),r(r({},"kit","Kit"),"kit-duotone","Kit Duotone"),[1,2,3,4,5,6,7,8,9,10]),J=M.concat([11,12,13,14,15,16,17,18,19,20]),F=[].concat(t(Object.keys({classic:["fas","far","fal","fat","fad"],duotone:["fadr","fadl","fadt"],sharp:["fass","fasr","fasl","fast"],"sharp-duotone":["fasds","fasdr","fasdl","fasdt"],slab:["faslr"],"slab-press":["faslpr"],whiteboard:["fawsb"],thumbprint:["fatl"],notdog:["fans"],"notdog-duo":["fands"],etch:["faes"],jelly:["fajr"],"jelly-fill":["fajfr"],"jelly-duo":["fajdr"],chisel:["facr"]})),["solid","regular","light","thin","duotone","brands","semibold"],["aw","fw","pull-left","pull-right"],["2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","inverse","layers","layers-bottom-left","layers-bottom-right","layers-counter","layers-text","layers-top-left","layers-top-right","li","pull-end","pull-start","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul","width-auto","width-fixed",F,S,N,P]).concat(M.map(function(t){return"".concat(t,"x")})).concat(J.map(function(t){return"w-".concat(t)})),U=(()=>{try{return"production"===process.env.NODE_ENV}catch(t){return!1}})();function I(t){return new Proxy(t,{get:function(t,e){return e in t?t[e]:t[O]}})}S=l({},s),S[O]=l(l(l(l({},{"fa-duotone":"duotone"}),s[O]),i),o),I(S),N=l({},{chisel:{regular:"facr"},classic:{brands:"fab",light:"fal",regular:"far",solid:"fas",thin:"fat"},duotone:{light:"fadl",regular:"fadr",solid:"fad",thin:"fadt"},etch:{solid:"faes"},jelly:{regular:"fajr"},"jelly-duo":{regular:"fajdr"},"jelly-fill":{regular:"fajfr"},notdog:{solid:"fans"},"notdog-duo":{solid:"fands"},sharp:{light:"fasl",regular:"fasr",solid:"fass",thin:"fast"},"sharp-duotone":{light:"fasdl",regular:"fasdr",solid:"fasds",thin:"fasdt"},slab:{regular:"faslr"},"slab-press":{regular:"faslpr"},thumbprint:{light:"fatl"},whiteboard:{semibold:"fawsb"}}),N[O]=l(l(l(l({},{duotone:"fad"}),N[O]),z),_),I(N),P=l({},{classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"},slab:{faslr:"fa-regular"},"slab-press":{faslpr:"fa-regular"},whiteboard:{fawsb:"fa-semibold"},thumbprint:{fatl:"fa-light"},notdog:{fans:"fa-solid"},"notdog-duo":{fands:"fa-solid"},etch:{faes:"fa-solid"},jelly:{fajr:"fa-regular"},"jelly-fill":{fajfr:"fa-regular"},"jelly-duo":{fajdr:"fa-regular"},chisel:{facr:"fa-regular"}}),P[O]=l(l({},P[O]),{fak:"fa-kit"}),I(P),M=l({},{classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"},slab:{"fa-regular":"faslr"},"slab-press":{"fa-regular":"faslpr"},whiteboard:{"fa-semibold":"fawsb"},thumbprint:{"fa-light":"fatl"},notdog:{"fa-solid":"fans"},"notdog-duo":{"fa-solid":"fands"},etch:{"fa-solid":"faes"},jelly:{"fa-regular":"fajr"},"jelly-fill":{"fa-regular":"fajfr"},"jelly-duo":{"fa-regular":"fajdr"},chisel:{"fa-regular":"facr"}});M[O]=l(l({},M[O]),{"fa-kit":"fak"}),I(M),I(l({},{classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"},slab:{400:"faslr"},"slab-press":{400:"faslpr"},whiteboard:{600:"fawsb"},thumbprint:{300:"fatl"},notdog:{900:"fans"},"notdog-duo":{900:"fands"},etch:{900:"faes"},chisel:{400:"facr"},jelly:{400:"fajr"},"jelly-fill":{400:"fajfr"},"jelly-duo":{400:"fajdr"}})),[].concat(t(["kit"]),t(F));!function(t){try{for(var e=arguments.length,o=new Array(1<e?e-1:0),n=1;n<e;n++)o[n-1]=arguments[n];t.apply(void 0,o)}catch(t){if(!U)throw t}}(function(){u&&h&&L(window.FontAwesomeDetection.report)})});