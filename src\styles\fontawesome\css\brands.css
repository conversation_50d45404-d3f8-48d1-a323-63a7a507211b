/*!
 * Font Awesome Free 7.0.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2025 Fonticons, Inc.
 */
:root, :host {
  --fa-family-brands: "Font Awesome 7 Brands";
  --fa-font-brands: normal 400 1em/1 var(--fa-family-brands);
}

@font-face {
  font-family: "Font Awesome 7 Brands";
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("../webfonts/fa-brands-400.woff2");
}
.fab,
.fa-brands,
.fa-classic.fa-brands {
  --fa-family: var(--fa-family-brands);
  --fa-style: 400;
}

.fa-firefox-browser {
  --fa: "\e007";
}

.fa-ideal {
  --fa: "\e013";
}

.fa-microblog {
  --fa: "\e01a";
}

.fa-square-pied-piper {
  --fa: "\e01e";
}

.fa-pied-piper-square {
  --fa: "\e01e";
}

.fa-unity {
  --fa: "\e049";
}

.fa-dailymotion {
  --fa: "\e052";
}

.fa-square-instagram {
  --fa: "\e055";
}

.fa-instagram-square {
  --fa: "\e055";
}

.fa-mixer {
  --fa: "\e056";
}

.fa-shopify {
  --fa: "\e057";
}

.fa-deezer {
  --fa: "\e077";
}

.fa-edge-legacy {
  --fa: "\e078";
}

.fa-google-pay {
  --fa: "\e079";
}

.fa-rust {
  --fa: "\e07a";
}

.fa-tiktok {
  --fa: "\e07b";
}

.fa-unsplash {
  --fa: "\e07c";
}

.fa-cloudflare {
  --fa: "\e07d";
}

.fa-guilded {
  --fa: "\e07e";
}

.fa-hive {
  --fa: "\e07f";
}

.fa-42-group {
  --fa: "\e080";
}

.fa-innosoft {
  --fa: "\e080";
}

.fa-instalod {
  --fa: "\e081";
}

.fa-octopus-deploy {
  --fa: "\e082";
}

.fa-perbyte {
  --fa: "\e083";
}

.fa-uncharted {
  --fa: "\e084";
}

.fa-watchman-monitoring {
  --fa: "\e087";
}

.fa-wodu {
  --fa: "\e088";
}

.fa-wirsindhandwerk {
  --fa: "\e2d0";
}

.fa-wsh {
  --fa: "\e2d0";
}

.fa-bots {
  --fa: "\e340";
}

.fa-cmplid {
  --fa: "\e360";
}

.fa-bilibili {
  --fa: "\e3d9";
}

.fa-golang {
  --fa: "\e40f";
}

.fa-pix {
  --fa: "\e43a";
}

.fa-sitrox {
  --fa: "\e44a";
}

.fa-hashnode {
  --fa: "\e499";
}

.fa-meta {
  --fa: "\e49b";
}

.fa-padlet {
  --fa: "\e4a0";
}

.fa-nfc-directional {
  --fa: "\e530";
}

.fa-nfc-symbol {
  --fa: "\e531";
}

.fa-screenpal {
  --fa: "\e570";
}

.fa-space-awesome {
  --fa: "\e5ac";
}

.fa-square-font-awesome {
  --fa: "\e5ad";
}

.fa-square-gitlab {
  --fa: "\e5ae";
}

.fa-gitlab-square {
  --fa: "\e5ae";
}

.fa-odysee {
  --fa: "\e5c6";
}

.fa-stubber {
  --fa: "\e5c7";
}

.fa-debian {
  --fa: "\e60b";
}

.fa-shoelace {
  --fa: "\e60c";
}

.fa-threads {
  --fa: "\e618";
}

.fa-square-threads {
  --fa: "\e619";
}

.fa-square-x-twitter {
  --fa: "\e61a";
}

.fa-x-twitter {
  --fa: "\e61b";
}

.fa-opensuse {
  --fa: "\e62b";
}

.fa-letterboxd {
  --fa: "\e62d";
}

.fa-square-letterboxd {
  --fa: "\e62e";
}

.fa-mintbit {
  --fa: "\e62f";
}

.fa-google-scholar {
  --fa: "\e63b";
}

.fa-brave {
  --fa: "\e63c";
}

.fa-brave-reverse {
  --fa: "\e63d";
}

.fa-pixiv {
  --fa: "\e640";
}

.fa-upwork {
  --fa: "\e641";
}

.fa-webflow {
  --fa: "\e65c";
}

.fa-signal-messenger {
  --fa: "\e663";
}

.fa-bluesky {
  --fa: "\e671";
}

.fa-jxl {
  --fa: "\e67b";
}

.fa-square-upwork {
  --fa: "\e67c";
}

.fa-web-awesome {
  --fa: "\e682";
}

.fa-square-web-awesome {
  --fa: "\e683";
}

.fa-square-web-awesome-stroke {
  --fa: "\e684";
}

.fa-dart-lang {
  --fa: "\e693";
}

.fa-flutter {
  --fa: "\e694";
}

.fa-files-pinwheel {
  --fa: "\e69f";
}

.fa-css {
  --fa: "\e6a2";
}

.fa-square-bluesky {
  --fa: "\e6a3";
}

.fa-openai {
  --fa: "\e7cf";
}

.fa-square-linkedin {
  --fa: "\e7d0";
}

.fa-cash-app {
  --fa: "\e7d4";
}

.fa-disqus {
  --fa: "\e7d5";
}

.fa-eleventy {
  --fa: "\e7d6";
}

.fa-11ty {
  --fa: "\e7d6";
}

.fa-kakao-talk {
  --fa: "\e7d7";
}

.fa-linktree {
  --fa: "\e7d8";
}

.fa-notion {
  --fa: "\e7d9";
}

.fa-pandora {
  --fa: "\e7da";
}

.fa-pixelfed {
  --fa: "\e7db";
}

.fa-tidal {
  --fa: "\e7dc";
}

.fa-vsco {
  --fa: "\e7dd";
}

.fa-w3c {
  --fa: "\e7de";
}

.fa-lumon {
  --fa: "\e7e2";
}

.fa-lumon-drop {
  --fa: "\e7e3";
}

.fa-square-figma {
  --fa: "\e7e4";
}

.fa-tex {
  --fa: "\e7ff";
}

.fa-duolingo {
  --fa: "\e812";
}

.fa-square-twitter {
  --fa: "\f081";
}

.fa-twitter-square {
  --fa: "\f081";
}

.fa-square-facebook {
  --fa: "\f082";
}

.fa-facebook-square {
  --fa: "\f082";
}

.fa-linkedin {
  --fa: "\f08c";
}

.fa-square-github {
  --fa: "\f092";
}

.fa-github-square {
  --fa: "\f092";
}

.fa-twitter {
  --fa: "\f099";
}

.fa-facebook {
  --fa: "\f09a";
}

.fa-github {
  --fa: "\f09b";
}

.fa-pinterest {
  --fa: "\f0d2";
}

.fa-square-pinterest {
  --fa: "\f0d3";
}

.fa-pinterest-square {
  --fa: "\f0d3";
}

.fa-square-google-plus {
  --fa: "\f0d4";
}

.fa-google-plus-square {
  --fa: "\f0d4";
}

.fa-google-plus-g {
  --fa: "\f0d5";
}

.fa-linkedin-in {
  --fa: "\f0e1";
}

.fa-github-alt {
  --fa: "\f113";
}

.fa-maxcdn {
  --fa: "\f136";
}

.fa-html5 {
  --fa: "\f13b";
}

.fa-css3 {
  --fa: "\f13c";
}

.fa-btc {
  --fa: "\f15a";
}

.fa-youtube {
  --fa: "\f167";
}

.fa-xing {
  --fa: "\f168";
}

.fa-square-xing {
  --fa: "\f169";
}

.fa-xing-square {
  --fa: "\f169";
}

.fa-dropbox {
  --fa: "\f16b";
}

.fa-stack-overflow {
  --fa: "\f16c";
}

.fa-instagram {
  --fa: "\f16d";
}

.fa-flickr {
  --fa: "\f16e";
}

.fa-adn {
  --fa: "\f170";
}

.fa-bitbucket {
  --fa: "\f171";
}

.fa-tumblr {
  --fa: "\f173";
}

.fa-square-tumblr {
  --fa: "\f174";
}

.fa-tumblr-square {
  --fa: "\f174";
}

.fa-apple {
  --fa: "\f179";
}

.fa-windows {
  --fa: "\f17a";
}

.fa-android {
  --fa: "\f17b";
}

.fa-linux {
  --fa: "\f17c";
}

.fa-dribbble {
  --fa: "\f17d";
}

.fa-skype {
  --fa: "\f17e";
}

.fa-foursquare {
  --fa: "\f180";
}

.fa-trello {
  --fa: "\f181";
}

.fa-gratipay {
  --fa: "\f184";
}

.fa-vk {
  --fa: "\f189";
}

.fa-weibo {
  --fa: "\f18a";
}

.fa-renren {
  --fa: "\f18b";
}

.fa-pagelines {
  --fa: "\f18c";
}

.fa-stack-exchange {
  --fa: "\f18d";
}

.fa-square-vimeo {
  --fa: "\f194";
}

.fa-vimeo-square {
  --fa: "\f194";
}

.fa-slack {
  --fa: "\f198";
}

.fa-slack-hash {
  --fa: "\f198";
}

.fa-wordpress {
  --fa: "\f19a";
}

.fa-openid {
  --fa: "\f19b";
}

.fa-yahoo {
  --fa: "\f19e";
}

.fa-google {
  --fa: "\f1a0";
}

.fa-reddit {
  --fa: "\f1a1";
}

.fa-square-reddit {
  --fa: "\f1a2";
}

.fa-reddit-square {
  --fa: "\f1a2";
}

.fa-stumbleupon-circle {
  --fa: "\f1a3";
}

.fa-stumbleupon {
  --fa: "\f1a4";
}

.fa-delicious {
  --fa: "\f1a5";
}

.fa-digg {
  --fa: "\f1a6";
}

.fa-pied-piper-pp {
  --fa: "\f1a7";
}

.fa-pied-piper-alt {
  --fa: "\f1a8";
}

.fa-drupal {
  --fa: "\f1a9";
}

.fa-joomla {
  --fa: "\f1aa";
}

.fa-behance {
  --fa: "\f1b4";
}

.fa-square-behance {
  --fa: "\f1b5";
}

.fa-behance-square {
  --fa: "\f1b5";
}

.fa-steam {
  --fa: "\f1b6";
}

.fa-square-steam {
  --fa: "\f1b7";
}

.fa-steam-square {
  --fa: "\f1b7";
}

.fa-spotify {
  --fa: "\f1bc";
}

.fa-deviantart {
  --fa: "\f1bd";
}

.fa-soundcloud {
  --fa: "\f1be";
}

.fa-vine {
  --fa: "\f1ca";
}

.fa-codepen {
  --fa: "\f1cb";
}

.fa-jsfiddle {
  --fa: "\f1cc";
}

.fa-rebel {
  --fa: "\f1d0";
}

.fa-empire {
  --fa: "\f1d1";
}

.fa-square-git {
  --fa: "\f1d2";
}

.fa-git-square {
  --fa: "\f1d2";
}

.fa-git {
  --fa: "\f1d3";
}

.fa-hacker-news {
  --fa: "\f1d4";
}

.fa-tencent-weibo {
  --fa: "\f1d5";
}

.fa-qq {
  --fa: "\f1d6";
}

.fa-weixin {
  --fa: "\f1d7";
}

.fa-slideshare {
  --fa: "\f1e7";
}

.fa-twitch {
  --fa: "\f1e8";
}

.fa-yelp {
  --fa: "\f1e9";
}

.fa-paypal {
  --fa: "\f1ed";
}

.fa-google-wallet {
  --fa: "\f1ee";
}

.fa-cc-visa {
  --fa: "\f1f0";
}

.fa-cc-mastercard {
  --fa: "\f1f1";
}

.fa-cc-discover {
  --fa: "\f1f2";
}

.fa-cc-amex {
  --fa: "\f1f3";
}

.fa-cc-paypal {
  --fa: "\f1f4";
}

.fa-cc-stripe {
  --fa: "\f1f5";
}

.fa-lastfm {
  --fa: "\f202";
}

.fa-square-lastfm {
  --fa: "\f203";
}

.fa-lastfm-square {
  --fa: "\f203";
}

.fa-ioxhost {
  --fa: "\f208";
}

.fa-angellist {
  --fa: "\f209";
}

.fa-buysellads {
  --fa: "\f20d";
}

.fa-connectdevelop {
  --fa: "\f20e";
}

.fa-dashcube {
  --fa: "\f210";
}

.fa-forumbee {
  --fa: "\f211";
}

.fa-leanpub {
  --fa: "\f212";
}

.fa-sellsy {
  --fa: "\f213";
}

.fa-shirtsinbulk {
  --fa: "\f214";
}

.fa-simplybuilt {
  --fa: "\f215";
}

.fa-skyatlas {
  --fa: "\f216";
}

.fa-pinterest-p {
  --fa: "\f231";
}

.fa-whatsapp {
  --fa: "\f232";
}

.fa-viacoin {
  --fa: "\f237";
}

.fa-medium {
  --fa: "\f23a";
}

.fa-medium-m {
  --fa: "\f23a";
}

.fa-y-combinator {
  --fa: "\f23b";
}

.fa-optin-monster {
  --fa: "\f23c";
}

.fa-opencart {
  --fa: "\f23d";
}

.fa-expeditedssl {
  --fa: "\f23e";
}

.fa-cc-jcb {
  --fa: "\f24b";
}

.fa-cc-diners-club {
  --fa: "\f24c";
}

.fa-creative-commons {
  --fa: "\f25e";
}

.fa-gg {
  --fa: "\f260";
}

.fa-gg-circle {
  --fa: "\f261";
}

.fa-odnoklassniki {
  --fa: "\f263";
}

.fa-square-odnoklassniki {
  --fa: "\f264";
}

.fa-odnoklassniki-square {
  --fa: "\f264";
}

.fa-get-pocket {
  --fa: "\f265";
}

.fa-wikipedia-w {
  --fa: "\f266";
}

.fa-safari {
  --fa: "\f267";
}

.fa-chrome {
  --fa: "\f268";
}

.fa-firefox {
  --fa: "\f269";
}

.fa-opera {
  --fa: "\f26a";
}

.fa-internet-explorer {
  --fa: "\f26b";
}

.fa-contao {
  --fa: "\f26d";
}

.fa-500px {
  --fa: "\f26e";
}

.fa-amazon {
  --fa: "\f270";
}

.fa-houzz {
  --fa: "\f27c";
}

.fa-vimeo-v {
  --fa: "\f27d";
}

.fa-black-tie {
  --fa: "\f27e";
}

.fa-fonticons {
  --fa: "\f280";
}

.fa-reddit-alien {
  --fa: "\f281";
}

.fa-edge {
  --fa: "\f282";
}

.fa-codiepie {
  --fa: "\f284";
}

.fa-modx {
  --fa: "\f285";
}

.fa-fort-awesome {
  --fa: "\f286";
}

.fa-usb {
  --fa: "\f287";
}

.fa-product-hunt {
  --fa: "\f288";
}

.fa-mixcloud {
  --fa: "\f289";
}

.fa-scribd {
  --fa: "\f28a";
}

.fa-bluetooth {
  --fa: "\f293";
}

.fa-bluetooth-b {
  --fa: "\f294";
}

.fa-gitlab {
  --fa: "\f296";
}

.fa-wpbeginner {
  --fa: "\f297";
}

.fa-wpforms {
  --fa: "\f298";
}

.fa-envira {
  --fa: "\f299";
}

.fa-glide {
  --fa: "\f2a5";
}

.fa-glide-g {
  --fa: "\f2a6";
}

.fa-viadeo {
  --fa: "\f2a9";
}

.fa-square-viadeo {
  --fa: "\f2aa";
}

.fa-viadeo-square {
  --fa: "\f2aa";
}

.fa-snapchat {
  --fa: "\f2ab";
}

.fa-snapchat-ghost {
  --fa: "\f2ab";
}

.fa-square-snapchat {
  --fa: "\f2ad";
}

.fa-snapchat-square {
  --fa: "\f2ad";
}

.fa-pied-piper {
  --fa: "\f2ae";
}

.fa-first-order {
  --fa: "\f2b0";
}

.fa-yoast {
  --fa: "\f2b1";
}

.fa-themeisle {
  --fa: "\f2b2";
}

.fa-google-plus {
  --fa: "\f2b3";
}

.fa-font-awesome {
  --fa: "\f2b4";
}

.fa-font-awesome-flag {
  --fa: "\f2b4";
}

.fa-font-awesome-logo-full {
  --fa: "\f2b4";
}

.fa-linode {
  --fa: "\f2b8";
}

.fa-quora {
  --fa: "\f2c4";
}

.fa-free-code-camp {
  --fa: "\f2c5";
}

.fa-telegram {
  --fa: "\f2c6";
}

.fa-telegram-plane {
  --fa: "\f2c6";
}

.fa-bandcamp {
  --fa: "\f2d5";
}

.fa-grav {
  --fa: "\f2d6";
}

.fa-etsy {
  --fa: "\f2d7";
}

.fa-imdb {
  --fa: "\f2d8";
}

.fa-ravelry {
  --fa: "\f2d9";
}

.fa-sellcast {
  --fa: "\f2da";
}

.fa-superpowers {
  --fa: "\f2dd";
}

.fa-wpexplorer {
  --fa: "\f2de";
}

.fa-meetup {
  --fa: "\f2e0";
}

.fa-square-font-awesome-stroke {
  --fa: "\f35c";
}

.fa-font-awesome-alt {
  --fa: "\f35c";
}

.fa-accessible-icon {
  --fa: "\f368";
}

.fa-accusoft {
  --fa: "\f369";
}

.fa-adversal {
  --fa: "\f36a";
}

.fa-affiliatetheme {
  --fa: "\f36b";
}

.fa-algolia {
  --fa: "\f36c";
}

.fa-amilia {
  --fa: "\f36d";
}

.fa-angrycreative {
  --fa: "\f36e";
}

.fa-app-store {
  --fa: "\f36f";
}

.fa-app-store-ios {
  --fa: "\f370";
}

.fa-apper {
  --fa: "\f371";
}

.fa-asymmetrik {
  --fa: "\f372";
}

.fa-audible {
  --fa: "\f373";
}

.fa-avianex {
  --fa: "\f374";
}

.fa-aws {
  --fa: "\f375";
}

.fa-bimobject {
  --fa: "\f378";
}

.fa-bitcoin {
  --fa: "\f379";
}

.fa-bity {
  --fa: "\f37a";
}

.fa-blackberry {
  --fa: "\f37b";
}

.fa-blogger {
  --fa: "\f37c";
}

.fa-blogger-b {
  --fa: "\f37d";
}

.fa-buromobelexperte {
  --fa: "\f37f";
}

.fa-centercode {
  --fa: "\f380";
}

.fa-cloudscale {
  --fa: "\f383";
}

.fa-cloudsmith {
  --fa: "\f384";
}

.fa-cloudversify {
  --fa: "\f385";
}

.fa-cpanel {
  --fa: "\f388";
}

.fa-css3-alt {
  --fa: "\f38b";
}

.fa-cuttlefish {
  --fa: "\f38c";
}

.fa-d-and-d {
  --fa: "\f38d";
}

.fa-deploydog {
  --fa: "\f38e";
}

.fa-deskpro {
  --fa: "\f38f";
}

.fa-digital-ocean {
  --fa: "\f391";
}

.fa-discord {
  --fa: "\f392";
}

.fa-discourse {
  --fa: "\f393";
}

.fa-dochub {
  --fa: "\f394";
}

.fa-docker {
  --fa: "\f395";
}

.fa-draft2digital {
  --fa: "\f396";
}

.fa-square-dribbble {
  --fa: "\f397";
}

.fa-dribbble-square {
  --fa: "\f397";
}

.fa-dyalog {
  --fa: "\f399";
}

.fa-earlybirds {
  --fa: "\f39a";
}

.fa-erlang {
  --fa: "\f39d";
}

.fa-facebook-f {
  --fa: "\f39e";
}

.fa-facebook-messenger {
  --fa: "\f39f";
}

.fa-firstdraft {
  --fa: "\f3a1";
}

.fa-fonticons-fi {
  --fa: "\f3a2";
}

.fa-fort-awesome-alt {
  --fa: "\f3a3";
}

.fa-freebsd {
  --fa: "\f3a4";
}

.fa-gitkraken {
  --fa: "\f3a6";
}

.fa-gofore {
  --fa: "\f3a7";
}

.fa-goodreads {
  --fa: "\f3a8";
}

.fa-goodreads-g {
  --fa: "\f3a9";
}

.fa-google-drive {
  --fa: "\f3aa";
}

.fa-google-play {
  --fa: "\f3ab";
}

.fa-gripfire {
  --fa: "\f3ac";
}

.fa-grunt {
  --fa: "\f3ad";
}

.fa-gulp {
  --fa: "\f3ae";
}

.fa-square-hacker-news {
  --fa: "\f3af";
}

.fa-hacker-news-square {
  --fa: "\f3af";
}

.fa-hire-a-helper {
  --fa: "\f3b0";
}

.fa-hotjar {
  --fa: "\f3b1";
}

.fa-hubspot {
  --fa: "\f3b2";
}

.fa-itunes {
  --fa: "\f3b4";
}

.fa-itunes-note {
  --fa: "\f3b5";
}

.fa-jenkins {
  --fa: "\f3b6";
}

.fa-joget {
  --fa: "\f3b7";
}

.fa-js {
  --fa: "\f3b8";
}

.fa-square-js {
  --fa: "\f3b9";
}

.fa-js-square {
  --fa: "\f3b9";
}

.fa-keycdn {
  --fa: "\f3ba";
}

.fa-kickstarter {
  --fa: "\f3bb";
}

.fa-square-kickstarter {
  --fa: "\f3bb";
}

.fa-kickstarter-k {
  --fa: "\f3bc";
}

.fa-laravel {
  --fa: "\f3bd";
}

.fa-line {
  --fa: "\f3c0";
}

.fa-lyft {
  --fa: "\f3c3";
}

.fa-magento {
  --fa: "\f3c4";
}

.fa-medapps {
  --fa: "\f3c6";
}

.fa-medrt {
  --fa: "\f3c8";
}

.fa-microsoft {
  --fa: "\f3ca";
}

.fa-mix {
  --fa: "\f3cb";
}

.fa-mizuni {
  --fa: "\f3cc";
}

.fa-monero {
  --fa: "\f3d0";
}

.fa-napster {
  --fa: "\f3d2";
}

.fa-node-js {
  --fa: "\f3d3";
}

.fa-npm {
  --fa: "\f3d4";
}

.fa-ns8 {
  --fa: "\f3d5";
}

.fa-nutritionix {
  --fa: "\f3d6";
}

.fa-page4 {
  --fa: "\f3d7";
}

.fa-palfed {
  --fa: "\f3d8";
}

.fa-patreon {
  --fa: "\f3d9";
}

.fa-periscope {
  --fa: "\f3da";
}

.fa-phabricator {
  --fa: "\f3db";
}

.fa-phoenix-framework {
  --fa: "\f3dc";
}

.fa-playstation {
  --fa: "\f3df";
}

.fa-pushed {
  --fa: "\f3e1";
}

.fa-python {
  --fa: "\f3e2";
}

.fa-red-river {
  --fa: "\f3e3";
}

.fa-wpressr {
  --fa: "\f3e4";
}

.fa-rendact {
  --fa: "\f3e4";
}

.fa-replyd {
  --fa: "\f3e6";
}

.fa-resolving {
  --fa: "\f3e7";
}

.fa-rocketchat {
  --fa: "\f3e8";
}

.fa-rockrms {
  --fa: "\f3e9";
}

.fa-schlix {
  --fa: "\f3ea";
}

.fa-searchengin {
  --fa: "\f3eb";
}

.fa-servicestack {
  --fa: "\f3ec";
}

.fa-sistrix {
  --fa: "\f3ee";
}

.fa-speakap {
  --fa: "\f3f3";
}

.fa-staylinked {
  --fa: "\f3f5";
}

.fa-steam-symbol {
  --fa: "\f3f6";
}

.fa-sticker-mule {
  --fa: "\f3f7";
}

.fa-studiovinari {
  --fa: "\f3f8";
}

.fa-supple {
  --fa: "\f3f9";
}

.fa-uber {
  --fa: "\f402";
}

.fa-uikit {
  --fa: "\f403";
}

.fa-uniregistry {
  --fa: "\f404";
}

.fa-untappd {
  --fa: "\f405";
}

.fa-ussunnah {
  --fa: "\f407";
}

.fa-vaadin {
  --fa: "\f408";
}

.fa-viber {
  --fa: "\f409";
}

.fa-vimeo {
  --fa: "\f40a";
}

.fa-vnv {
  --fa: "\f40b";
}

.fa-square-whatsapp {
  --fa: "\f40c";
}

.fa-whatsapp-square {
  --fa: "\f40c";
}

.fa-whmcs {
  --fa: "\f40d";
}

.fa-wordpress-simple {
  --fa: "\f411";
}

.fa-xbox {
  --fa: "\f412";
}

.fa-yandex {
  --fa: "\f413";
}

.fa-yandex-international {
  --fa: "\f414";
}

.fa-apple-pay {
  --fa: "\f415";
}

.fa-cc-apple-pay {
  --fa: "\f416";
}

.fa-fly {
  --fa: "\f417";
}

.fa-node {
  --fa: "\f419";
}

.fa-osi {
  --fa: "\f41a";
}

.fa-react {
  --fa: "\f41b";
}

.fa-autoprefixer {
  --fa: "\f41c";
}

.fa-less {
  --fa: "\f41d";
}

.fa-sass {
  --fa: "\f41e";
}

.fa-vuejs {
  --fa: "\f41f";
}

.fa-angular {
  --fa: "\f420";
}

.fa-aviato {
  --fa: "\f421";
}

.fa-ember {
  --fa: "\f423";
}

.fa-gitter {
  --fa: "\f426";
}

.fa-hooli {
  --fa: "\f427";
}

.fa-strava {
  --fa: "\f428";
}

.fa-stripe {
  --fa: "\f429";
}

.fa-stripe-s {
  --fa: "\f42a";
}

.fa-typo3 {
  --fa: "\f42b";
}

.fa-amazon-pay {
  --fa: "\f42c";
}

.fa-cc-amazon-pay {
  --fa: "\f42d";
}

.fa-ethereum {
  --fa: "\f42e";
}

.fa-korvue {
  --fa: "\f42f";
}

.fa-elementor {
  --fa: "\f430";
}

.fa-square-youtube {
  --fa: "\f431";
}

.fa-youtube-square {
  --fa: "\f431";
}

.fa-flipboard {
  --fa: "\f44d";
}

.fa-hips {
  --fa: "\f452";
}

.fa-php {
  --fa: "\f457";
}

.fa-quinscape {
  --fa: "\f459";
}

.fa-readme {
  --fa: "\f4d5";
}

.fa-java {
  --fa: "\f4e4";
}

.fa-pied-piper-hat {
  --fa: "\f4e5";
}

.fa-creative-commons-by {
  --fa: "\f4e7";
}

.fa-creative-commons-nc {
  --fa: "\f4e8";
}

.fa-creative-commons-nc-eu {
  --fa: "\f4e9";
}

.fa-creative-commons-nc-jp {
  --fa: "\f4ea";
}

.fa-creative-commons-nd {
  --fa: "\f4eb";
}

.fa-creative-commons-pd {
  --fa: "\f4ec";
}

.fa-creative-commons-pd-alt {
  --fa: "\f4ed";
}

.fa-creative-commons-remix {
  --fa: "\f4ee";
}

.fa-creative-commons-sa {
  --fa: "\f4ef";
}

.fa-creative-commons-sampling {
  --fa: "\f4f0";
}

.fa-creative-commons-sampling-plus {
  --fa: "\f4f1";
}

.fa-creative-commons-share {
  --fa: "\f4f2";
}

.fa-creative-commons-zero {
  --fa: "\f4f3";
}

.fa-ebay {
  --fa: "\f4f4";
}

.fa-keybase {
  --fa: "\f4f5";
}

.fa-mastodon {
  --fa: "\f4f6";
}

.fa-r-project {
  --fa: "\f4f7";
}

.fa-researchgate {
  --fa: "\f4f8";
}

.fa-teamspeak {
  --fa: "\f4f9";
}

.fa-first-order-alt {
  --fa: "\f50a";
}

.fa-fulcrum {
  --fa: "\f50b";
}

.fa-galactic-republic {
  --fa: "\f50c";
}

.fa-galactic-senate {
  --fa: "\f50d";
}

.fa-jedi-order {
  --fa: "\f50e";
}

.fa-mandalorian {
  --fa: "\f50f";
}

.fa-old-republic {
  --fa: "\f510";
}

.fa-phoenix-squadron {
  --fa: "\f511";
}

.fa-sith {
  --fa: "\f512";
}

.fa-trade-federation {
  --fa: "\f513";
}

.fa-wolf-pack-battalion {
  --fa: "\f514";
}

.fa-hornbill {
  --fa: "\f592";
}

.fa-mailchimp {
  --fa: "\f59e";
}

.fa-megaport {
  --fa: "\f5a3";
}

.fa-nimblr {
  --fa: "\f5a8";
}

.fa-rev {
  --fa: "\f5b2";
}

.fa-shopware {
  --fa: "\f5b5";
}

.fa-squarespace {
  --fa: "\f5be";
}

.fa-themeco {
  --fa: "\f5c6";
}

.fa-weebly {
  --fa: "\f5cc";
}

.fa-wix {
  --fa: "\f5cf";
}

.fa-ello {
  --fa: "\f5f1";
}

.fa-hackerrank {
  --fa: "\f5f7";
}

.fa-kaggle {
  --fa: "\f5fa";
}

.fa-markdown {
  --fa: "\f60f";
}

.fa-neos {
  --fa: "\f612";
}

.fa-zhihu {
  --fa: "\f63f";
}

.fa-alipay {
  --fa: "\f642";
}

.fa-the-red-yeti {
  --fa: "\f69d";
}

.fa-critical-role {
  --fa: "\f6c9";
}

.fa-d-and-d-beyond {
  --fa: "\f6ca";
}

.fa-dev {
  --fa: "\f6cc";
}

.fa-fantasy-flight-games {
  --fa: "\f6dc";
}

.fa-wizards-of-the-coast {
  --fa: "\f730";
}

.fa-think-peaks {
  --fa: "\f731";
}

.fa-reacteurope {
  --fa: "\f75d";
}

.fa-artstation {
  --fa: "\f77a";
}

.fa-atlassian {
  --fa: "\f77b";
}

.fa-canadian-maple-leaf {
  --fa: "\f785";
}

.fa-centos {
  --fa: "\f789";
}

.fa-confluence {
  --fa: "\f78d";
}

.fa-dhl {
  --fa: "\f790";
}

.fa-diaspora {
  --fa: "\f791";
}

.fa-fedex {
  --fa: "\f797";
}

.fa-fedora {
  --fa: "\f798";
}

.fa-figma {
  --fa: "\f799";
}

.fa-intercom {
  --fa: "\f7af";
}

.fa-invision {
  --fa: "\f7b0";
}

.fa-jira {
  --fa: "\f7b1";
}

.fa-mendeley {
  --fa: "\f7b3";
}

.fa-raspberry-pi {
  --fa: "\f7bb";
}

.fa-redhat {
  --fa: "\f7bc";
}

.fa-sketch {
  --fa: "\f7c6";
}

.fa-sourcetree {
  --fa: "\f7d3";
}

.fa-suse {
  --fa: "\f7d6";
}

.fa-ubuntu {
  --fa: "\f7df";
}

.fa-ups {
  --fa: "\f7e0";
}

.fa-usps {
  --fa: "\f7e1";
}

.fa-yarn {
  --fa: "\f7e3";
}

.fa-airbnb {
  --fa: "\f834";
}

.fa-battle-net {
  --fa: "\f835";
}

.fa-bootstrap {
  --fa: "\f836";
}

.fa-buffer {
  --fa: "\f837";
}

.fa-chromecast {
  --fa: "\f838";
}

.fa-evernote {
  --fa: "\f839";
}

.fa-itch-io {
  --fa: "\f83a";
}

.fa-salesforce {
  --fa: "\f83b";
}

.fa-speaker-deck {
  --fa: "\f83c";
}

.fa-symfony {
  --fa: "\f83d";
}

.fa-waze {
  --fa: "\f83f";
}

.fa-yammer {
  --fa: "\f840";
}

.fa-git-alt {
  --fa: "\f841";
}

.fa-stackpath {
  --fa: "\f842";
}

.fa-cotton-bureau {
  --fa: "\f89e";
}

.fa-buy-n-large {
  --fa: "\f8a6";
}

.fa-mdb {
  --fa: "\f8ca";
}

.fa-orcid {
  --fa: "\f8d2";
}

.fa-swift {
  --fa: "\f8e1";
}

.fa-umbraco {
  --fa: "\f8e8";
}