import { useEffect, useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useDispatch, useSelector } from "react-redux";
import { loginUser } from "../store/slices/authSlice";
import "../styles/scss/authForms.css";
import { HashLoader } from "react-spinners";
import { toast } from "react-toastify";
import Logo from "../assets/logo.png";
const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

const StudentLogin = () => {
  const { isLoading } = useSelector((state) => state.auth);
  // const [isLoading, setIsLoading] = useState(false);
  // const [error, setError] = useState("");
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data) => {
    // setIsLoading(true);
    // setError("");

    try {
      const result = await dispatch(
        loginUser({
          email: data.email,
          password: data.password,
          type: "student",
        })
      ).unwrap();
      if (!result.verified) {
        navigate("/code-verify");
        toast(result.message);
      } else {
        navigate("/student");
      }
    } catch (error) {
      // setError(error);
      // console.log(error);
      toast.error(error || "Something went wrong!");
    }
    // setIsLoading(false);
  };
  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto ",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
          borderRadius: "50%",
          backgroundColor: "transparent",
        }}
      />
    );
  }
  return (
    <div className="auth-page">
      <div className="form-card">
        <div className="image">
          <img src={Logo} alt="login-logo" />
        </div>
        <div>
          <h2 className="form-title">Student Sign In</h2>
          <p className="form-subtitle">
            Sign in to access your student dashboard
          </p>
        </div>
        <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email address
            </label>
            <input
              {...register("email")}
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              className="form-control"
              placeholder="Email address"
            />
            {errors.email && (
              <p className="form-error">{errors.email.message}</p>
            )}
          </div>
          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Password
            </label>
            <input
              {...register("password")}
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              className="form-control"
              placeholder="Password"
            />
            {errors.password && (
              <p className="form-error">{errors.password.message}</p>
            )}
          </div>

          {/* {error && <div className="form-error text-center">{error}</div>} */}

          <div className="form-actions">
            <button
              type="submit"
              // disabled={isLoading}
              className="btn btn-block"
            >
              {/* {isLoading ? "Signing in..." : "Sign in"} */}
              Sign in
            </button>
          </div>

          <div className="text-center">
            <Link to="/register">Don't have an account? Register</Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StudentLogin;
