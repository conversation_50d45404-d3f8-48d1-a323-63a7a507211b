import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import api from "../../services/api";
import "../../styles/studentResult.css";
import NoResults from "../../assets/no-exams.png";
import { HashLoader } from "react-spinners";
import { CheckCircle } from "lucide-react";
const StudentResults = () => {
  const [attempts, setAttempts] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  useEffect(() => {
    fetchAttempts();
  }, []);

  const fetchAttempts = async () => {
    try {
      const response = await api.get("/my-attempts");
      setAttempts(response.data.data || []);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching attempts:", error);
      setError("Failed to load results");
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto ",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
          borderRadius: "50%",
          backgroundColor: "transparent",
        }}
      />
    );
  }

  if (error) {
    return (
      <div className="form-error" style={{ textAlign: "center" }}>
        <p style={{ fontWeight: 700, marginBottom: ".5rem" }}>Error</p>
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div className="student-result">
      <h1 className="available-result-title">
        <CheckCircle size={28} color="#1c2a45" />
        My Results
      </h1>

      {attempts.length === 0 ? (
        <div className="no-result">
          <p>No attempts found. Start a quiz to see your results here.</p>
          <div className="image">
            <img src={NoResults} alt="no-available-result" />
          </div>
        </div>
      ) : (
        <div className="table-wrap results-wrap">
          <div className="table-container">
            <table className="table">
              <thead className="results-head">
                <tr>
                  <th>Type</th>
                  <th>Subject</th>
                  <th>Started</th>
                  <th>Status</th>
                  <th>Score</th>
                  <th>Action</th>
                </tr>
              </thead>
              <tbody>
                {attempts.map((attempt) => {
                  const submitted = Boolean(attempt.submitted_at);
                  return (
                    <tr key={attempt.id}>
                      <td>{attempt.type === "exam" ? "Exam" : "Quiz"}</td>
                      <td>
                        {attempt.subject?.name || attempt.exam?.subject?.name}
                      </td>
                      <td>{new Date(attempt.started_at).toLocaleString()}</td>
                      <td>
                        {submitted ? (
                          <span className="badge success">Submitted</span>
                        ) : (
                          <span className="badge warn">In Progress</span>
                        )}
                      </td>
                      <td>{submitted ? `${attempt.score}%` : "-"}</td>
                      <td className="results-cell-right">
                        {submitted ? (
                          <button
                            className="btn btn-success"
                            onClick={() =>
                              navigate(`/student/attempt-details/${attempt.id}`)
                            }
                          >
                            View Details
                          </button>
                        ) : (
                          <button
                            className="btn"
                            onClick={() =>
                              navigate(`/student/attempt/${attempt.id}`)
                            }
                          >
                            Continue Quiz
                          </button>
                        )}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>

          <div className="table-pagination">
            <span>{attempts.length} result(s)</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default StudentResults;
