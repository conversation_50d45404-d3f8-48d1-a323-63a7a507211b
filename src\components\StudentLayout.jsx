import { Link } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { logout } from "../store/slices/authSlice";
import {
  LogOut,
  BookOpen,
  GraduationCap,
  BarChart3,
  HelpCircle,
} from "lucide-react";
import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAnglesRight } from "@fortawesome/free-solid-svg-icons";
import "../styles/layout.css";
import Logo from "../assets/logo.png";
const StudentLayout = ({ children }) => {
  const user = useSelector((state) => state.auth.user);
  const dispatch = useDispatch();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = () => {
    dispatch(logout());
  };

  return (
    <div className="layout">
      {/* Header */}
      <header className="navbar">
        <div className="nav-left">
          {/* <h1 className="brand">Exams</h1> */}
          <div className="image">
            <img src={Logo} alt="header-logo" />
          </div>
        </div>
        <span className="welcome">{user?.name}</span>
        <div className="nav-right">
          <button onClick={handleLogout} className="logout-btn">
            <LogOut size={18} />
            Logout
          </button>
        </div>
      </header>

      <div className="main">
        {/* Sidebar */}
        <nav className={`sidebar ${sidebarOpen ? "open" : "closed"}`}>
          <button
            className="sidebar-toggle"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            <FontAwesomeIcon icon={faAnglesRight} />
          </button>
          <h1 className="sidebar-title">
            Edus <span>Ex</span>amin
          </h1>
          <ul>
            <li>
              <Link
                to="/student"
                onClick={() => {
                  setSidebarOpen(false);
                }}
              >
                <BarChart3 size={18} />
                Dashboard
              </Link>
            </li>
            <li>
              <Link
                to="/student/start-quiz"
                onClick={() => {
                  setSidebarOpen(false);
                }}
              >
                <HelpCircle size={18} />
                Start Quiz
              </Link>
            </li>
            <li>
              <Link
                to="/student/exams"
                onClick={() => {
                  setSidebarOpen(false);
                }}
              >
                <GraduationCap size={18} />
                Available Exams
              </Link>
            </li>
            <li>
              <Link
                to="/student/results"
                onClick={() => {
                  setSidebarOpen(false);
                }}
              >
                <BookOpen size={18} />
                My Results
              </Link>
            </li>
          </ul>
        </nav>

        {/* Main content */}
        <main className="content">{children}</main>
      </div>
    </div>
  );
};

export default StudentLayout;
