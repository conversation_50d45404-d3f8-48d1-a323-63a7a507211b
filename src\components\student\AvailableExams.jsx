import { useEffect, useState } from "react";
import api from "../../services/api";
import { useNavigate } from "react-router-dom";
import "../../styles/availableExams.css";
import NoExams from "../../assets/no-exams.png";
import { HashLoader } from "react-spinners";

const AvailableExams = () => {
  const [exams, setExams] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const load = async () => {
    setIsLoading(true);
    setError("");
    try {
      const res = await api.get("/student/exams/available");
      const data = res.data || [];
      setExams(data);
    } catch (e) {
      console.error(e);
      setError("Failed to load available exams");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    load();
  }, []);

  const isRegistered = (e) =>
    Array.isArray(e.registrations) && e.registrations.length > 0;

  const register = async (id) => {
    try {
      await api.post(`/student/exams/${id}/register`);
      await load();
    } catch (e) {
      console.error(e);
      alert(e.response?.data?.message || "Failed to register");
    }
  };

  const unregister = async (id) => {
    try {
      await api.delete(`/student/exams/${id}/register`);
      await load();
    } catch (e) {
      console.error(e);
      alert(e.response?.data?.message || "Failed to unregister");
    }
  };

  const startExam = async (e) => {
    try {
      const examId = e.id;
      const resp = await api.post(`/student/exams/${examId}/attempt`);
      const attempt = resp.data;
      navigate(`/student/attempt/${attempt.id}`);
    } catch (err) {
      console.error(err);
      alert(err.response?.data?.message || "Failed to start exam");
    }
  };

  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto ",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
          borderRadius: "50%",
          backgroundColor: "transparent",
        }}
      />
    );
  }

  return (
    <div className="available-exams">
      <h1 className="available-exams-title">Available Exams</h1>

      {error && (
        <div
          className="form-error"
          style={{ textAlign: "left", marginBottom: "1rem" }}
        >
          {error}
        </div>
      )}

      {exams.length === 0 ? (
        <div className="no-exams">
          <p>No exams available at the moment.</p>
          <div className="image">
            <img src={NoExams} alt="no-available-exams" />
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {exams.map((e) => (
            <div key={e.id} className="exam-card">
              <div
                className="flex exam-row"
                style={{
                  alignItems: "flex-start",
                  justifyContent: "space-between",
                }}
              >
                <div className="flex-1" style={{ paddingRight: "1rem" }}>
                  <h3 className="card-title">{e.title}</h3>
                  <p className="card-subtitle">{e.subject?.name}</p>
                  <p style={{ color: "var(--muted)" }}>{e.description}</p>
                  <div className="exam-meta">
                    <div>
                      <strong>Start:</strong>{" "}
                      {new Date(e.start_time).toLocaleString()}
                    </div>
                    <div>
                      <strong>End:</strong>{" "}
                      {new Date(e.end_time).toLocaleString()}
                    </div>
                    <div>
                      <strong>Duration:</strong> {e.duration} min
                    </div>
                  </div>
                </div>
                <div className="exam-actions">
                  {isRegistered(e) ? (
                    <>
                      <span className="badge success">Registered</span>
                      <button onClick={() => startExam(e)} className="btn">
                        Start Exam
                      </button>
                      <button
                        onClick={() => unregister(e.id)}
                        className="btn btn-outline btn-danger-outline"
                      >
                        Unregister
                      </button>
                    </>
                  ) : (
                    <button
                      onClick={() => register(e.id)}
                      className="btn btn-success"
                    >
                      Register
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AvailableExams;
