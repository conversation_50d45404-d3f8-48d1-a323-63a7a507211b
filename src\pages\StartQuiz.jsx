import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import api from "../services/api";
import "../styles/startQuiz.css";
import { Play, Search } from "lucide-react";
import { BarLoader } from "react-spinners";
import { toast } from "react-toastify";
const startQuizSchema = z.object({
  subjectId: z.string().min(1, "Please select a subject"),
  questionCount: z.number().min(1).max(50),
});

const StartQuiz = () => {
  const [subjects, setSubjects] = useState([]);
  const [filteredSubjects, setFilteredSubjects] = useState([]);
  const [selectedSubject, setSelectedSubject] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(startQuizSchema),
    defaultValues: {
      questionCount: 10,
    },
  });

  useEffect(() => {
    fetchSubjects();
  }, []);

  useEffect(() => {
    const filtered = subjects.filter((subject) =>
      subject.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredSubjects(filtered);
  }, [subjects, searchTerm]);

  const fetchSubjects = async () => {
    try {
      const response = await api.get("/subjects");
      setSubjects(response.data.data || response.data);
      setIsLoading(false);
    } catch (error) {
      setError("Failed to load subjects");
      setIsLoading(false);
    }
  };

  const handleSubjectSelect = (subject) => {
    setSelectedSubject(subject);
    setValue("subjectId", subject.id.toString());
    setValue("questionCount", Math.min(10, subject.questions_count || 10));
  };

  const onSubmit = async (data) => {
    setIsLoading(true);
    setError("");

    try {
      const response = await api.post(
        `/subjects/${data.subjectId}/quiz`,
        {
          question_count: data.questionCount || 10,
        }
      );
      const attempt = response.data.data || response.data;
      navigate(`/student/attempt/${attempt.id}`);
    } catch (error) {
      setError(error.response?.data?.message || "Failed to start quiz");
    }

    setIsLoading(false);
  };

  useEffect(() => {
    if (error) {
      toast.error(error || "Something went wrong!");
    }
  }, [error]);
  // if (isLoading) {
  //   return (
  //     <HashLoader
  //       color="#1c2a45"
  //       size={50}
  //       cssOverride={{
  //         display: "block",
  //         margin: "0px auto",
  //         position: "absolute",
  //         left: "50%",
  //         top: "50%",
  //         transform: "translate(-50%, -50%)",
  //       }}
  //     />
  //   );
  // }

  return (
    <div className="quiz-container">
      <div className="quiz-header">
        <h1 className="quiz-title">
          <Play size={28} color="#1c2a45" />
          Start a Quiz
        </h1>
        <div className="input-container">
          <Search size={18} color="#867575" />
          <input
            type="text"
            placeholder="Search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="quiz-form">
        <div className="question-section">
          <label htmlFor="questionCount" className="section-label">
            Number of Questions{" "}
            {selectedSubject
              ? `(1-${selectedSubject.questions_count || 10})`
              : "(1-50)"}
          </label>
          <input
            {...register("questionCount", { valueAsNumber: true })}
            type="number"
            placeholder="number of questions"
            id="questionCount"
            min="1"
            max={selectedSubject ? selectedSubject.questions_count || 10 : 50}
            className="number-input"
          />
          {errors.questionCount && (
            <p className="error-text">{errors.questionCount.message}</p>
          )}
        </div>
        <div className="subject-section">
          <label className="section-label">Select Subject</label>
          <div className="subject-list">
            {isLoading ? (
              <BarLoader
                color="#080808"
                loading={true}
                width={100}
                height={4}
                cssOverride={{
                  display: "block",
                  margin: "12px 0",
                }}
              />
            ) : (
              filteredSubjects.map((subject) => (
                <div
                  key={subject.id}
                  onClick={() => handleSubjectSelect(subject)}
                  className={`subject-item ${
                    selectedSubject?.id === subject.id ? "selected" : ""
                  }`}
                >
                  <input
                    type="radio"
                    name="subjectId"
                    value={subject.id}
                    checked={selectedSubject?.id === subject.id}
                    onChange={() => handleSubjectSelect(subject)}
                    className="radio-select"
                  />
                  <div className="subject-info">
                    <span className="subject-name">{subject.name}</span>
                    <span className="subject-count">
                      ({subject.questions_count || 0} questions available)
                    </span>
                  </div>
                </div>
              ))
            )}
          </div>
          {errors.subjectId && (
            <p className="error-text">{errors.subjectId.message}</p>
          )}
        </div>

        {error && <div className="error-box">{error}</div>}

        <div className="submit-section">
          <button className="btn" type="submit" disabled={isLoading}>
            <div className="original">{isLoading ? "Starting.." : "Start"}</div>
            <div className="letters">
              <span>N</span>
              <span>O</span>
              <span>W</span>
            </div>
          </button>
        </div>
      </form>
    </div>
  );
};

export default StartQuiz;
