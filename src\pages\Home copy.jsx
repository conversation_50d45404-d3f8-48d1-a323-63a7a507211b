import { <PERSON> } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, Users, BarChart3 } from "lucide-react";
import "../styles/scss/home.css";
import Logo from "../assets/logo.svg";
import { motion } from "framer-motion";
/**
 * A Home page component for EduExamin.
 * This component renders a hero section with a logo, a heading, and a button group.
 * It also renders a grid section with three cards, each with a heading and a list of items.
 * The component uses the motion library to animate the elements as they come into view.
 * @returns {JSX.Element} A React component.
 */
const Home = () => {
  return (
    <div className="page fade-in">

      <div className="container">
        
        <div className="home-header">
          <div className="info">
            <motion.h1
              initial={{ opacity: 0, x: -200 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <span className="whole-title"> <img src={Logo} alt="landing-image" className="title-logo" /> Edu<span className="special-color">Ex</span>amin</span>
            </motion.h1>

            <p className="form-subtitle">
              Quiz to Boost🚀Your Skills✅
            </p>
            <div className="btn-group">
              <Link to="/login" className="get-started student">
                Login
              </Link>
              <Link to="/register" className="get-started">
                Register
              </Link>
            </div>
          </div>
          <div className="image">
            <img src={Logo} alt="landing-image" />
          </div>
        </div>

        <div className="grid grid-3">
          <motion.div
            className="card"
            initial={{ opacity: 0, x: -200 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            viewport={{ once: true, amount: 0.2 }}
          >
            {/* <div className="card"> */}
            <div className="card-header">
              <BookOpen className="icon icon-accent" />
              <h3>For Students</h3>
            </div>
            <ul className="space-y-2">
              <li>Take exams online</li>
              <li>View results instantly</li>
              <li>Track your progress</li>
            </ul>
            {/* </div> */}
          </motion.div>
          <motion.div
            className="card"
            initial={{ opacity: 0, x: 200 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
            viewport={{ once: true, amount: 0.2 }}
          >
            {/* <div className="card"> */}
            <div className="card-header">
              <BarChart3 className="icon icon-purple" />
              <h3>Features</h3>
            </div>
            <ul className="space-y-2">
              <li>Multiple question types</li>
              <li>Real-time exam monitoring</li>
              <li>Comprehensive analytics</li>
            </ul>
            {/* </div> */}
          </motion.div>
        </div>

      </div>
    </div>
  );
};

export default Home;
