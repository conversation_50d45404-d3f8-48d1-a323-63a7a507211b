import { Link, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import { logout } from "../store/slices/authSlice";
import {
  LogOut,
  User,
  BookOpen,
  FileQuestion,
  GraduationCap,
  BarChart3,
} from "lucide-react";
import "../styles/layout.css";
import Logo from "../assets/logo.png";
import { useState } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faAnglesRight } from "@fortawesome/free-solid-svg-icons";

const AdminLayout = ({ children }) => {
  const user = useSelector((state) => state.auth.user);
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  return (
    <div className="layout">
      {/* Header */}
      <header className="navbar">
        <div className="nav-left">
          <div className="image">
            <img src={Logo} alt="header-logo" />
          </div>
        </div>

        <span className="welcome">{user?.name}</span>
        <div className="nav-right" style={{ gap: "0.75rem" }}>
          <button onClick={handleLogout} className="logout-btn">
            <LogOut className="w-4 h-4" />
            Logout
          </button>
        </div>
      </header>

      <div className="main">
        {/* Sidebar */}
        <nav className={`sidebar ${sidebarOpen ? "open" : "closed"}`}>
          <button
            className="sidebar-toggle"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            <FontAwesomeIcon icon={faAnglesRight} />
          </button>
          <h1 className="sidebar-title">
            Edus <span>Ex</span>amin
          </h1>
          <ul>
            <li>
              <Link
                to="/admin"
                onClick={() => {
                  setSidebarOpen(false);
                }}
              >
                <BarChart3 size={18} />
                Dashboard
              </Link>
            </li>
            <li>
              <Link
                to="/admin/subjects"
                onClick={() => {
                  setSidebarOpen(false);
                }}
              >
                <BookOpen size={18} />
                Subjects
              </Link>
            </li>
            <li>
              <Link
                to="/admin/questions"
                onClick={() => {
                  setSidebarOpen(false);
                }}
              >
                <FileQuestion size={18} />
                Questions
              </Link>
            </li>
            <li>
              <Link
                to="/admin/exams"
                onClick={() => {
                  setSidebarOpen(false);
                }}
              >
                <GraduationCap size={18} />
                Exams
              </Link>
            </li>
            <li>
              <Link
                to="/admin/students"
                onClick={() => {
                  setSidebarOpen(false);
                }}
              >
                <User size={18} />
                Students
              </Link>
            </li>
          </ul>
        </nav>

        {/* Main content */}
        <main className="page">{children}</main>
      </div>
    </div>
  );
};

export default AdminLayout;
