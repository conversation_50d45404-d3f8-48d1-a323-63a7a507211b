import { Routes, Route } from "react-router-dom";
import StudentLayout from "../components/StudentLayout";
import StudentHome from "../components/student/StudentHome";
import AvailableExams from "../components/student/AvailableExams";
import StudentResults from "../components/student/StudentResults";
import StartQuiz from "./StartQuiz";
import AttemptQuiz from "./AttemptQuiz";
import AttemptDetails from "./AttemptDetails";
// import "../styles/studentDashboard.css";

const StudentDashboard = () => {
  return (
    <StudentLayout>
      <Routes>
        <Route path="/" element={<StudentHome />} />
        <Route path="/exams" element={<AvailableExams />} />
        <Route path="/results" element={<StudentResults />} />
        <Route path="/start-quiz" element={<StartQuiz />} />
        <Route path="/attempt/:id" element={<AttemptQuiz />} />
        <Route path="/attempt-details/:id" element={<AttemptDetails />} />
      </Routes>
    </StudentLayout>
  );
};

export default StudentDashboard;
