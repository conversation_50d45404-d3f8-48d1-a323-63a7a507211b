import { useEffect, useState } from "react";
import api from "../../services/api";
import "../../styles/adminHome.css";
import { HashLoader } from "react-spinners";
import { Book, FileText, Users, HelpCircle } from "lucide-react";
import { Link } from "react-router-dom";

const AdminHome = () => {
  const [stats, setStats] = useState({
    total_subjects: 0,
    total_questions: 0,
    total_exams: 0,
    total_students: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await api.get("/admin/analytics/dashboard");
        setStats(response.data);
        console.log(response.data);
      } catch (error) {
        console.error("Failed to fetch dashboard stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
    );
  }

  return (
    <div className="admin-home">
      <div className="header">
        <h1 className="title">Admin Dashboard</h1>
        <p className="subtitle">Welcome to the admin panel</p>
      </div>

      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-content">
            <div className="icon-wrapper blue">
              <Book size={24} />
            </div>
            <div className="stat-text">
              <p className="label">Total Subjects</p>
              <p className="value">{stats.total_subjects}</p>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-content">
            <div className="icon-wrapper green">
              <HelpCircle size={24} />
            </div>
            <div className="stat-text">
              <p className="label">Total Questions</p>
              <p className="value">{stats.total_questions}</p>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-content">
            <div className="icon-wrapper purple">
              <FileText size={24} />
            </div>
            <div className="stat-text">
              <p className="label">Total Exams</p>
              <p className="value">{stats.total_exams}</p>
            </div>
          </div>
        </div>

        <div className="stat-card">
          <div className="stat-content">
            <div className="icon-wrapper orange">
              <Users size={24} />
            </div>
            <div className="stat-text">
              <p className="label">Total Students</p>
              <p className="value">{stats.total_students}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="quick-actions">
        <h2 className="section-title">Quick Actions</h2>
        <div className="actions-grid">
          <Link to="/admin/subjects" className="action-btn blue">
            Manage Subjects
          </Link>
          <Link to="/admin/questions" className="action-btn green">
            Create Questions
          </Link>
          <Link to="/admin/exams" className="action-btn purple">
            Manage Exams
          </Link>
          <Link to="/admin/students" className="action-btn orange">
            View Students
          </Link>
        </div>
      </div>
    </div>
  );
};

export default AdminHome;
