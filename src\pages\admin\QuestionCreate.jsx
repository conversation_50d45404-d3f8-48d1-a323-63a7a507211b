import { useEffect, useMemo, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import api from "../../services/api";
import { ArrowLeft, Plus, Trash2 } from "lucide-react";
import "../../styles/question-create.css";
const defaultOption = () => ({
  option_text: "",
  direction: "ltr",
  is_correct: false,
});

const QuestionCreate = () => {
  const navigate = useNavigate();
  const { id: subjectIdFromRoute } = useParams();

  const [subjects, setSubjects] = useState([]);
  const [form, setForm] = useState({
    subject_id: subjectIdFromRoute ? Number(subjectIdFromRoute) : "",
    question_text: "",
    direction: "ltr",
    type: "mcq",
    options: [
      defaultOption(),
      defaultOption(),
      defaultOption(),
      defaultOption(),
    ],
  });
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const load = async () => {
      try {
        const res = await api.get("/admin/subjects");
        setSubjects(res.data.data || res.data || []);
      } catch (e) {
        console.error(e);
        setError("Failed to load subjects");
      }
    };
    load();
  }, []);

  const isTrueFalse = form.type === "true_false";

  useEffect(() => {
    if (isTrueFalse) {
      setForm((prev) => ({
        ...prev,
        options: [
          { option_text: "True", direction: "ltr", is_correct: true },
          { option_text: "False", direction: "ltr", is_correct: false },
        ],
      }));
    }
  }, [isTrueFalse]);

  const canSubmit = useMemo(() => {
    if (!form.subject_id || !form.question_text.trim()) return false;
    if (form.type === "mcq") {
      const validOptions = form.options.filter(
        (o) => o.option_text.trim() !== ""
      );
      if (validOptions.length < 2) return false;
      const anyCorrect = validOptions.some((o) => o.is_correct);
      return anyCorrect;
    }
    if (form.type === "true_false") {
      return (
        form.options.length === 2 && form.options.some((o) => o.is_correct)
      );
    }
    return true;
  }, [form]);

  const updateOption = (idx, key, value) => {
    setForm((prev) => ({
      ...prev,
      options: prev.options.map((o, i) =>
        i === idx ? { ...o, [key]: value } : o
      ),
    }));
  };

  const addOption = () =>
    setForm((prev) => ({
      ...prev,
      options: [...prev.options, defaultOption()],
    }));
  const removeOption = (idx) =>
    setForm((prev) => ({
      ...prev,
      options: prev.options.filter((_, i) => i !== idx),
    }));

  const onSubmit = async (e) => {
    e.preventDefault();
    if (!canSubmit) return;
    setSaving(true);
    setError("");
    try {
      const payload = {
        subject_id: Number(form.subject_id),
        question_text: form.question_text.trim(),
        direction: form.direction,
        type: form.type,
        options: form.options
          .filter((o) => o.option_text.trim() !== "")
          .map((o) => ({
            option_text: o.option_text.trim(),
            direction: o.direction,
            is_correct: !!o.is_correct,
          })),
      };
      await api.post("/admin/questions", payload);
      // Navigate back to subject details if coming from that context
      if (subjectIdFromRoute) {
        navigate(`/admin/subjects/${subjectIdFromRoute}`);
      } else {
        navigate("/admin/questions");
      }
    } catch (e) {
      console.error(e);
      setError(e.response?.data?.message || "Failed to create question");
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="create-question-page">
      <div className="page-header">
        <div className="header-group">
          <h1 className="page-title">Create Question</h1>
          <button
            onClick={() =>
              subjectIdFromRoute
                ? navigate(`/admin/subjects/${subjectIdFromRoute}`)
                : navigate("/admin/questions")
            }
            className="btn btn-back"
          >
            <ArrowLeft className="btn-icon" />
            Back
          </button>
        </div>
      </div>

      {error && <div className="alert alert-error">{error}</div>}

      <form onSubmit={onSubmit} className="form-card">
        <div className="form-grid">
          <div className="form-group">
            <label className="form-label">Subject</label>
            <select
              value={form.subject_id}
              onChange={(e) =>
                setForm((p) => ({ ...p, subject_id: e.target.value }))
              }
              className="form-select"
            >
              <option value="">Select subject</option>
              {subjects.map((s) => (
                <option key={s.id} value={s.id}>
                  {s.name}
                </option>
              ))}
            </select>
          </div>
          <div className="form-group">
            <label className="form-label">Direction</label>
            <select
              value={form.direction}
              onChange={(e) =>
                setForm((p) => ({ ...p, direction: e.target.value }))
              }
              className="form-select"
            >
              <option value="ltr">Left to Right</option>
              <option value="rtl">Right to Left</option>
            </select>
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">Type</label>
          <div className="radio-group">
            <label className="radio-option">
              <input
                type="radio"
                name="type"
                value="mcq"
                checked={form.type === "mcq"}
                onChange={() => setForm((p) => ({ ...p, type: "mcq" }))}
              />
              <span>Multiple Choice</span>
            </label>
            <label className="radio-option">
              <input
                type="radio"
                name="type"
                value="true_false"
                checked={form.type === "true_false"}
                onChange={() => setForm((p) => ({ ...p, type: "true_false" }))}
              />
              <span>True / False</span>
            </label>
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">Question Text</label>
          <textarea
            rows={3}
            value={form.question_text}
            onChange={(e) =>
              setForm((p) => ({ ...p, question_text: e.target.value }))
            }
            className="form-textarea"
            placeholder="Enter the question text"
          />
        </div>

        <div className="form-group">
          <div className="options-header">
            <label className="form-label">Options</label>
            {form.type === "mcq" && (
              <button
                type="button"
                onClick={addOption}
                className="btn btn-add-option"
              >
                <Plus className="btn-icon" /> Add Option
              </button>
            )}
          </div>
          <div className="options-list">
            {form.options.map((opt, idx) => (
              <div key={idx} className="option-row">
                <input
                  type="checkbox"
                  checked={!!opt.is_correct}
                  onChange={(e) =>
                    updateOption(idx, "is_correct", e.target.checked)
                  }
                  className="option-checkbox"
                  title="Mark as correct"
                />
                <div className="option-fields">
                  <input
                    type="text"
                    value={opt.option_text}
                    onChange={(e) =>
                      updateOption(idx, "option_text", e.target.value)
                    }
                    className="option-input"
                    placeholder={`Option ${idx + 1}`}
                    disabled={isTrueFalse}
                  />
                  <select
                    value={opt.direction}
                    onChange={(e) =>
                      updateOption(idx, "direction", e.target.value)
                    }
                    className="option-select"
                    disabled={isTrueFalse}
                  >
                    <option value="ltr">LTR</option>
                    <option value="rtl">RTL</option>
                  </select>
                  {form.type === "mcq" && (
                    <button
                      type="button"
                      onClick={() => removeOption(idx)}
                      className="btn btn-remove-option"
                      title="Remove option"
                    >
                      <Trash2 className="btn-icon" />
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="form-actions">
          <button
            type="button"
            onClick={() =>
              subjectIdFromRoute
                ? navigate(`/admin/subjects/${subjectIdFromRoute}`)
                : navigate("/admin/questions")
            }
            className="btn btn-cancel"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!canSubmit || saving}
            className="btn btn-submit"
          >
            {saving ? "Saving..." : "Create Question"}
          </button>
        </div>
      </form>
    </div>
  );
};

export default QuestionCreate;
