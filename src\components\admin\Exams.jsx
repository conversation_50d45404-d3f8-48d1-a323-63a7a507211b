import { useEffect, useMemo, useState } from "react";
import api from "../../services/api";
import { useNavigate } from "react-router-dom";
import { Plus, Edit, Trash2, Search } from "lucide-react";
import { HashLoader } from "react-spinners";
import "../../styles/exams.css";

const emptyForm = {
  subject_id: "",
  title: "",
  description: "",
  start_time: "",
  end_time: "",
  duration: 60,
  question_ids: [],
};

const Exams = () => {
  const navigate = useNavigate();
  const [exams, setExams] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [questions, setQuestions] = useState([]);
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    total: 0,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  const [showModal, setShowModal] = useState(false);
  const [form, setForm] = useState(emptyForm);
  const [editingId, setEditingId] = useState(null);

  // Question filters for the modal
  const [qSearch, setQSearch] = useState("");
  const [qSubject, setQSubject] = useState("");
  const [qType, setQType] = useState("");
  const [randomCount, setRandomCount] = useState("");

  // Wizard step state: 1) Basic info, 2) Schedule, 3) Questions
  const [currentStep, setCurrentStep] = useState(1);

  // Per-step validation
  const isStep1Valid = useMemo(() => {
    return Boolean(form.subject_id) && Boolean(form.title.trim());
  }, [form.subject_id, form.title]);

  const isStep2Valid = useMemo(() => {
    if (!form.start_time || !form.end_time || !form.duration) return false;
    return new Date(form.end_time) > new Date(form.start_time);
  }, [form.start_time, form.end_time, form.duration]);

  const nextStep = () => setCurrentStep((s) => Math.min(3, s + 1));
  const prevStep = () => setCurrentStep((s) => Math.max(1, s - 1));

  const loadData = async (page = 1) => {
    setIsLoading(true);
    setError("");
    try {
      const [subjectsRes, questionsRes, examsRes] = await Promise.all([
        api.get("/admin/subjects"),
        // per_page may be ignored by backend; included to try loading more items
        api.get("/admin/questions", { params: { page: 1, per_page: 200 } }),
        api.get("/admin/exams", { params: { page } }),
      ]);
      setSubjects(subjectsRes.data.data || subjectsRes.data || []);
      setQuestions(questionsRes.data.data || []);
      const data = examsRes.data;
      setExams(data.data || []);
      setPagination({
        current_page: data.current_page,
        last_page: data.last_page,
        total: data.total,
      });
    } catch (e) {
      console.error(e);
      setError("Failed to load exams");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadData(1);
  }, []);

  const isValid = useMemo(() => {
    // Final submission validity equals step 1 and step 2 validity
    return isStep1Valid && isStep2Valid;
  }, [isStep1Valid, isStep2Valid]);

  const openCreate = () => {
    setEditingId(null);
    setForm({ ...emptyForm });
    setQSearch("");
    setQSubject("");
    setQType("");
    setRandomCount("");
    setCurrentStep(1);
    setShowModal(true);
  };

  const openEdit = async (examId) => {
    try {
      const res = await api.get(`/admin/exams/${examId}`);
      const e = res.data;
      setEditingId(examId);
      setForm({
        subject_id: e.subject_id,
        title: e.title || "",
        description: e.description || "",
        start_time: e.start_time
          ? e.start_time.replace(" ", "T").slice(0, 16)
          : "",
        end_time: e.end_time ? e.end_time.replace(" ", "T").slice(0, 16) : "",
        duration: e.duration || 60,
        question_ids: (e.questions || []).map((q) => q.id),
      });
      // Pre-filter by the exam's subject to help admins
      setQSubject(String(e.subject_id || ""));
      setQSearch("");
      setQType("");
      setRandomCount("");
      setCurrentStep(1);
      setShowModal(true);
    } catch (err) {
      console.error(err);
      setError("Failed to load exam details");
    }
  };

  // Format datetime-local value to SQL-like local datetime string
  const toLocalSql = (value) => {
    if (!value) return null;
    const [date, time] = value.split("T");
    const [hh = "00", mm = "00", ss = "00"] = (time || "").split(":");
    return `${date} ${hh}:${mm}:${ss || "00"}`;
  };

  const onSubmit = async (e) => {
    e.preventDefault();
    if (!isValid) return;
    try {
      const payload = {
        subject_id: Number(form.subject_id),
        title: form.title.trim(),
        description: form.description || null,
        start_time: toLocalSql(form.start_time),
        end_time: toLocalSql(form.end_time),
        duration: Number(form.duration),
        question_ids: form.question_ids,
      };
      if (editingId) {
        await api.put(`/admin/exams/${editingId}`, payload);
      } else {
        await api.post("/admin/exams", payload);
      }
      setShowModal(false);
      setEditingId(null);
      await loadData(pagination.current_page);
    } catch (err) {
      console.error(err);
      const apiErrors = err.response?.data?.errors;
      if (apiErrors && typeof apiErrors === "object") {
        // Join first messages per field
        const messages = Object.entries(apiErrors).map(
          ([field, msgs]) =>
            `${field}: ${Array.isArray(msgs) ? msgs[0] : String(msgs)}`
        );
        setError(messages.join(" | "));
      } else {
        setError(err.response?.data?.message || "Failed to save exam");
      }
    }
  };

  const handleDelete = async (examId) => {
    if (!window.confirm("Delete this exam?")) return;
    try {
      await api.delete(`/admin/exams/${examId}`);
      await loadData(pagination.current_page);
    } catch (err) {
      console.error(err);
      setError("Failed to delete exam");
    }
  };

  const attachQuestion = async (examId, questionId) => {
    try {
      await api.post(`/admin/exams/${examId}/questions`, {
        question_ids: [questionId],
      });
      await loadData(pagination.current_page);
    } catch (err) {
      console.error(err);
      setError("Failed to attach question");
    }
  };

  const detachQuestion = async (examId, questionId) => {
    try {
      await api.delete(`/admin/exams/${examId}/questions/${questionId}`);
      await loadData(pagination.current_page);
    } catch (err) {
      console.error(err);
      setError("Failed to detach question");
    }
  };

  const canPrev = pagination.current_page > 1;
  const canNext = pagination.current_page < pagination.last_page;

  // Filter questions client-side for the modal
  const filteredQuestions = useMemo(() => {
    const search = qSearch.trim().toLowerCase();
    return (questions || []).filter((q) => {
      if (qSubject && String(q.subject_id) !== String(qSubject)) return false;
      if (qType && q.type !== qType) return false;
      if (!search) return true;
      return (q.question_text || "").toLowerCase().includes(search);
    });
  }, [questions, qSearch, qSubject, qType]);
  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
    );
  }
  return (
    <div className="exams-container">
      <div className="exams-header">
        <h1 className="exams-title">Exams Management</h1>
        <button onClick={openCreate} className="create-exam-button">
          <Plus className="create-icon" /> New Exam
        </button>
      </div>

      {error && <div className="error-message">{error}</div>}

      {isLoading ? (
        <div className="loading-wrapper">
          <div className="loading-spinner"></div>
        </div>
      ) : exams.length === 0 ? (
        <div className="no-exams-text">No exams found.</div>
      ) : (
        <div className="exam-list">
          {exams.map((e) => (
            <div key={e.id} className="exam-card">
              <div className="exam-card-header">
                <div className="exam-card-body">
                  <div className="exam-title-row">
                    <h3 className="exam-title">{e.title}</h3>
                    <span className="exam-subject">{e.subject?.name}</span>
                  </div>
                  <p className="exam-description">{e.description}</p>
                  <div className="exam-meta">
                    <div>
                      <strong>Start:</strong>{" "}
                      {new Date(e.start_time).toLocaleString()}
                    </div>
                    <div>
                      <strong>End:</strong>{" "}
                      {new Date(e.end_time).toLocaleString()}
                    </div>
                    <div>
                      <strong>Duration:</strong> {e.duration} min
                    </div>
                    <div>
                      <strong>Registrations:</strong>{" "}
                      {e.registrations_count ?? 0}
                    </div>
                  </div>
                </div>
                <div className="exam-actions">
                  <button
                    onClick={() => navigate(`/admin/exams/${e.id}`)}
                    className="details-button"
                  >
                    Details
                  </button>
                  <button
                    onClick={() => openEdit(e.id)}
                    className="edit-button"
                  >
                    <Edit className="action-icon" />
                  </button>
                  <button
                    onClick={() => handleDelete(e.id)}
                    className="delete-button"
                  >
                    <Trash2 className="action-icon" />
                  </button>
                </div>
              </div>
            </div>
          ))}

          <div className="pagination-row">
            <div className="pagination-info">
              Page {pagination.current_page} of {pagination.last_page} •{" "}
              {pagination.total} total
            </div>
            <div className="pagination-controls">
              <button
                disabled={!canPrev}
                onClick={() => canPrev && loadData(pagination.current_page - 1)}
                className={`pagination-button ${!canPrev ? "disabled" : ""}`}
              >
                Prev
              </button>
              <button
                disabled={!canNext}
                onClick={() => canNext && loadData(pagination.current_page + 1)}
                className={`pagination-button ${!canNext ? "disabled" : ""}`}
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}

      {showModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2 className="modal-title">
              {editingId ? "Edit Exam" : "Create Exam"}
            </h2>
            <form onSubmit={onSubmit} className="modal-form">
              <div className="step-indicator">
                <div className="step-tabs">
                  <span
                    className={`step-tab ${currentStep === 1 ? "active" : ""}`}
                  >
                    1. Basic
                  </span>
                  <span
                    className={`step-tab ${currentStep === 2 ? "active" : ""}`}
                  >
                    2. Schedule
                  </span>
                  <span
                    className={`step-tab ${currentStep === 3 ? "active" : ""}`}
                  >
                    3. Questions
                  </span>
                </div>
                <div className="step-count">Step {currentStep} of 3</div>
              </div>

              {currentStep === 1 && (
                <div className="step-body">
                  <div className="form-grid">
                    <div className="form-group">
                      <label className="form-label">Subject</label>
                      <select
                        value={form.subject_id}
                        onChange={(e) =>
                          setForm((p) => ({ ...p, subject_id: e.target.value }))
                        }
                        className="form-select"
                      >
                        <option value="">Select subject</option>
                        {subjects.map((s) => (
                          <option key={s.id} value={s.id}>
                            {s.name}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="form-group">
                      <label className="form-label">Title</label>
                      <input
                        type="text"
                        value={form.title}
                        onChange={(e) =>
                          setForm((p) => ({ ...p, title: e.target.value }))
                        }
                        className="form-input"
                      />
                    </div>
                  </div>

                  <div className="form-group">
                    <label className="form-label">Description</label>
                    <textarea
                      rows={3}
                      value={form.description}
                      onChange={(e) =>
                        setForm((p) => ({ ...p, description: e.target.value }))
                      }
                      className="form-textarea"
                    />
                  </div>
                </div>
              )}

              {/* ---------------------------------------------- */}
              {/* Step 2: Schedule */}
              {currentStep === 2 && (
                <div className="step-schedule">
                  <div className="schedule-grid">
                    <div className="schedule-group">
                      <label className="schedule-label">Start Time</label>
                      <input
                        type="datetime-local"
                        value={form.start_time}
                        onChange={(e) =>
                          setForm((p) => ({ ...p, start_time: e.target.value }))
                        }
                        className="schedule-input"
                      />
                    </div>
                    <div className="schedule-group">
                      <label className="schedule-label">End Time</label>
                      <input
                        type="datetime-local"
                        value={form.end_time}
                        onChange={(e) =>
                          setForm((p) => ({ ...p, end_time: e.target.value }))
                        }
                        className="schedule-input"
                      />
                    </div>
                    <div className="schedule-group">
                      <label className="schedule-label">
                        Duration (minutes)
                      </label>
                      <input
                        type="number"
                        value={form.duration}
                        onChange={(e) =>
                          setForm((p) => ({ ...p, duration: e.target.value }))
                        }
                        className="schedule-input"
                      />
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Questions */}
              {currentStep === 3 && (
                <div className="step-questions">
                  <div className="questions-header">
                    <label className="questions-label">Attach Questions</label>
                    <div className="questions-count">
                      Selected: {form.question_ids.length}
                    </div>
                  </div>

                  {/* Filters */}
                  <div className="questions-filters">
                    <div className="filters-grid">
                      <div className="filter-group">
                        <label className="filter-label">Subject</label>
                        <select
                          value={qSubject}
                          onChange={(e) => setQSubject(e.target.value)}
                          className="filter-select"
                        >
                          <option value="">All</option>
                          {subjects.map((s) => (
                            <option key={s.id} value={s.id}>
                              {s.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      <div className="filter-group">
                        <label className="filter-label">Type</label>
                        <select
                          value={qType}
                          onChange={(e) => setQType(e.target.value)}
                          className="filter-select"
                        >
                          <option value="">All</option>
                          <option value="mcq">Multiple Choice</option>
                          <option value="true_false">True / False</option>
                        </select>
                      </div>
                      <div className="filter-group search-group">
                        <label className="filter-label">Search</label>
                        <div className="search-row">
                          <input
                            type="text"
                            value={qSearch}
                            onChange={(e) => setQSearch(e.target.value)}
                            placeholder="Search question text..."
                            className="search-input"
                          />
                          <button
                            type="button"
                            onClick={async () => {
                              try {
                                const res = await api.get("/admin/questions", {
                                  params: {
                                    page: 1,
                                    per_page: 200,
                                    search: qSearch || undefined,
                                    subject_id: qSubject || undefined,
                                    type: qType || undefined,
                                  },
                                });
                                setQuestions(res.data.data || []);
                              } catch (err) {
                                console.error(err);
                              }
                            }}
                            className="search-button"
                          >
                            <Search className="search-icon" />
                            Search
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Random selection */}
                  <div className="random-select-row">
                    <label className="random-label">Select random</label>
                    <input
                      type="number"
                      min={0}
                      max={filteredQuestions.length}
                      value={randomCount}
                      onChange={(e) => setRandomCount(e.target.value)}
                      className="random-input"
                      placeholder="Count"
                    />
                    <button
                      type="button"
                      onClick={() => {
                        const n = Math.max(
                          0,
                          Math.min(
                            Number(randomCount || 0),
                            filteredQuestions.length
                          )
                        );
                        const ids = filteredQuestions.map((q) => q.id);
                        for (let i = ids.length - 1; i > 0; i--) {
                          const j = Math.floor(Math.random() * (i + 1));
                          [ids[i], ids[j]] = [ids[j], ids[i]];
                        }
                        const selected = ids.slice(0, n);
                        setForm((p) => ({ ...p, question_ids: selected }));
                      }}
                      className="random-button"
                    >
                      Apply
                    </button>
                  </div>

                  {/* Question cards */}
                  <div className="border rounded max-h-72 overflow-auto p-2 grid grid-cols-1 md:grid-cols-2 gap-2 bg-white">
                    {filteredQuestions.map((q) => {
                      const checked = form.question_ids.includes(q.id);
                      return (
                        <label
                          key={q.id}
                          className={`question-item ${
                            checked ? "selected" : ""
                          }`}
                        >
                          <div className="question-item-content">
                            <input
                              type="checkbox"
                              checked={checked}
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                setForm((p) => ({
                                  ...p,
                                  question_ids: isChecked
                                    ? [...p.question_ids, q.id]
                                    : p.question_ids.filter(
                                        (id) => id !== q.id
                                      ),
                                }));
                              }}
                              className="mt-1"
                            />
                            <div>
                              <div className="question-meta">
                                Q#{q.id} • {q.subject?.name} •{" "}
                                {q.type === "mcq" ? "MCQ" : "T/F"}
                              </div>
                              <div
                                className="question-text"
                                title={q.question_text}
                              >
                                {q.question_text}
                              </div>
                            </div>
                          </div>
                        </label>
                      );
                    })}
                    {filteredQuestions.length === 0 && (
                      <div className="no-questions-message">
                        No questions found for the selected filters.
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Footer actions: Back/Next/Submit */}
              <div className="form-footer">
                <div>
                  <button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="cancel-button"
                  >
                    Cancel
                  </button>
                </div>
                <div className="footer-actions">
                  {currentStep > 1 && (
                    <button
                      type="button"
                      onClick={prevStep}
                      className="back-button"
                    >
                      Back
                    </button>
                  )}
                  {currentStep < 3 && (
                    <button
                      type="button"
                      onClick={nextStep}
                      disabled={
                        (currentStep === 1 && !isStep1Valid) ||
                        (currentStep === 2 && !isStep2Valid)
                      }
                      className="next-button"
                    >
                      Next
                    </button>
                  )}
                  {currentStep === 3 && (
                    <button
                      type="submit"
                      disabled={!isStep1Valid || !isStep2Valid}
                      className="submit-button"
                    >
                      {editingId ? "Update Exam" : "Create Exam"}
                    </button>
                  )}
                </div>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Exams;
