import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import api from "../../services/api";
import { Plus, Edit, Trash2, Eye } from "lucide-react";
import "../../styles/subjects.css";
import { HashLoader } from "react-spinners";

const subjectSchema = z.object({
  name: z.string().min(1, "Subject name is required"),
  description: z.string().optional(),
});

const Subjects = () => {
  const [subjects, setSubjects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingSubject, setEditingSubject] = useState(null);
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(subjectSchema),
  });

  useEffect(() => {
    fetchSubjects();
  }, []);

  const fetchSubjects = async () => {
    try {
      const response = await api.get("/admin/subjects");
      setSubjects(response.data.data || response.data);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching subjects:", error);
      setError("Failed to load subjects");
      setIsLoading(false);
    }
  };

  const onSubmit = async (data) => {
    try {
      if (editingSubject) {
        await api.put(`/admin/subjects/${editingSubject.id}`, data);
      } else {
        await api.post("/admin/subjects", data);
      }
      fetchSubjects();
      setShowCreateModal(false);
      setEditingSubject(null);
      reset();
    } catch (error) {
      console.error("Error saving subject:", error);
      setError("Failed to save subject");
    }
  };

  const handleEdit = (subject) => {
    setEditingSubject(subject);
    reset({ name: subject.name, description: subject.description });
    setShowCreateModal(true);
  };

  const handleDelete = async (subjectId) => {
    if (window.confirm("Are you sure you want to delete this subject?")) {
      try {
        await api.delete(`/admin/subjects/${subjectId}`);
        fetchSubjects();
      } catch (error) {
        console.error("Error deleting subject:", error);
        setError("Failed to delete subject");
      }
    }
  };

  const handleViewDetails = (subjectId) => {
    navigate(`/admin/subjects/${subjectId}`);
  };

  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
    );
  }

  return (
    <div className="subjects-page">
      <div className="header-bar">
        <h1 className="page-title">Subjects Management</h1>
        <button
          onClick={() => {
            setEditingSubject(null);
            reset();
            setShowCreateModal(true);
          }}
          className="btn btn-primary"
        >
          <Plus className="btn-icon" />
          Add Subject
        </button>
      </div>

      {error && <div className="alert alert-error">{error}</div>}

      <div className="subjects-grid">
        {subjects.map((subject) => (
          <div key={subject.id} className="subject-card">
            <div className="card-header">
              <h3 className="subject-name">{subject.name}</h3>
              <div className="action-buttons">
                <button
                  onClick={() => handleViewDetails(subject.id)}
                  className="btn-icon btn-view"
                  title="View Details"
                >
                  <Eye className="btn-icon" />
                </button>
                <button
                  onClick={() => handleEdit(subject)}
                  className="btn-icon btn-edit"
                  title="Edit"
                >
                  <Edit className="btn-icon" />
                </button>
                <button
                  onClick={() => handleDelete(subject.id)}
                  className="btn-icon btn-delete"
                  title="Delete"
                >
                  <Trash2 className="btn-icon" />
                </button>
              </div>
            </div>
            <p className="subject-description">
              {subject.description || "No description"}
            </p>
            <div className="subject-meta">
              <span>{subject.questions_count || 0} questions</span>
              <span>{subject.exams_count || 0} exams</span>
            </div>
          </div>
        ))}
      </div>

      {subjects.length === 0 && (
        <div className="empty-state">
          <p className="empty-message">
            No subjects found. Create your first subject to get started.
          </p>
        </div>
      )}

      {/* Create/Edit Modal */}
      {showCreateModal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h2 className="modal-title">
              {editingSubject ? "Edit Subject" : "Create Subject"}
            </h2>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="form-group">
                <label className="form-label">Subject Name</label>
                <input
                  {...register("name")}
                  type="text"
                  className="form-input"
                  placeholder="Enter subject name"
                />
                {errors.name && (
                  <p className="form-error">{errors.name.message}</p>
                )}
              </div>
              <div className="form-group">
                <label className="form-label">Description (Optional)</label>
                <textarea
                  {...register("description")}
                  rows="3"
                  className="form-textarea"
                  placeholder="Enter subject description"
                />
              </div>
              <div className="form-actions">
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateModal(false);
                    setEditingSubject(null);
                    reset();
                  }}
                  className="btn btn-cancel"
                >
                  Cancel
                </button>
                <button type="submit" className="btn btn-submit">
                  {editingSubject ? "Update" : "Create"}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Subjects;
