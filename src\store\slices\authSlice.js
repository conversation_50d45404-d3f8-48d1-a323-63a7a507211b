import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import api, { TokenStorage } from "../../services/api";

// Async thunks for login and register
export const loginUser = createAsyncThunk(
  "auth/login",
  async ({ email, password, type }, { rejectWithValue }) => {
    try {
      const endpoint =
        type === "admin" ? "/auth/admin/login" : "/auth/student/login";
      const response = await api.post(endpoint, { email, password });
      const { user: userData, token, type: userType, verified } = response.data;
      console.log(response.data);
      const userWithType = { ...userData, type: userType };
      if (response.data.verified) {
        TokenStorage.set(token);
      }
      localStorage.setItem("user", JSON.stringify(userWithType));
      return { user: userWithType, token, verified };
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || "<PERSON><PERSON> failed");
    }
  }
);
// -----------------------------------------
export const registerUser = createAsyncThunk(
  "auth/register",
  async ({ data, type }, { rejectWithValue }) => {
    try {
      const endpoint =
        type === "admin" ? "/auth/admin/register" : "/auth/student/register";
      const response = await api.post(endpoint, data);
      const { user: userData, type: userType } = response.data;
      console.log(response.data);
      const userWithType = { ...userData, type: userType };
      // localStorage.setItem("token", token);
      localStorage.setItem("user", JSON.stringify(userWithType));
      return { user: userWithType };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Registration failed"
      );
    }
  }
);
// -----------------------------------------
export const verifyCode = createAsyncThunk(
  "auth/verifyCode",
  async ({ email, code }, { rejectWithValue }) => {
    try {
      const response = await api.post("/auth/student/verify-code", {
        email,
        code,
      });
      const { user: userData, token, type: userType } = response.data;
      const userWithType = { ...userData, type: userType };
      // Save to secure storage
      TokenStorage.set(token);
      localStorage.setItem("user", JSON.stringify(userWithType));
      console.log(response.data);
      return { user: userWithType, token };
    } catch (error) {
      return rejectWithValue(
        error.response?.data?.message || "Verification failed"
      );
    }
  }
);
// -----------------------------------------
const authSlice = createSlice({
  name: "auth",
  initialState: {
    user: null,
    token: null,
    isLoading: true,
    error: null,
  },
  reducers: {
    logout: (state) => {
      state.user = null;
      state.token = null;
      TokenStorage.remove();
    },
    setLoading: (state, action) => {
      state.isLoading = action.payload;
    },
    initializeAuth: (state) => {
      const storedToken = TokenStorage.get();
      const storedUser = localStorage.getItem("user");
      if (storedToken && storedUser) {
        state.token = storedToken;
        state.user = JSON.parse(storedUser);
      }

      state.isLoading = false;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.user = action.payload.user;
        if (action.payload.verified) {
          state.token = action.payload.token;
        }
        state.isLoading = false;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.error = action.payload;
        state.isLoading = false;
      })
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.user = action.payload.user;
        // state.token = action.payload.token;
        state.isLoading = false;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.error = action.payload;
        state.isLoading = false;
      })
      .addCase(verifyCode.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(verifyCode.fulfilled, (state, action) => {
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.isLoading = false;
      })
      .addCase(verifyCode.rejected, (state, action) => {
        state.error = action.payload;
        state.isLoading = false;
      });
  },
});

export const { logout, setLoading, initializeAuth } = authSlice.actions;
export default authSlice.reducer;
