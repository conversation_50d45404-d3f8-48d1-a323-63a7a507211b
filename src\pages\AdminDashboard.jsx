import { Routes, Route } from "react-router-dom";
import AdminLayout from "../components/AdminLayout";
import AdminHome from "../components/admin/AdminHome";
import Subjects from "../components/admin/Subjects";
import SubjectDetails from "./admin/SubjectDetails";
import Questions from "../components/admin/Questions";
import Exams from "../components/admin/Exams";
import Students from "../components/admin/Students";
import QuestionCreate from "./admin/QuestionCreate";
import QuestionEdit from "./admin/QuestionEdit";
import ExamDetails from "./admin/ExamDetails";
// import "../styles/adminDashboard.css";

const AdminDashboard = () => {
  return (
    <AdminLayout>
      <Routes>
        <Route path="/" element={<AdminHome />} />
        <Route path="/subjects" element={<Subjects />} />
        <Route path="/subjects/:id" element={<SubjectDetails />} />
        <Route
          path="/subjects/:id/questions/create"
          element={<QuestionCreate />}
        />
        <Route path="/questions" element={<Questions />} />
        <Route path="/questions/create" element={<QuestionCreate />} />
        <Route path="/questions/:id/edit" element={<QuestionEdit />} />
        <Route path="/exams" element={<Exams />} />
        <Route path="/exams/:id" element={<ExamDetails />} />
        <Route path="/students" element={<Students />} />
      </Routes>
    </AdminLayout>
  );
};

export default AdminDashboard;
