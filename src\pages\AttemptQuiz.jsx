import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import api from "../services/api";
import {
  BookOpen,
  ListOrdered,
  ArrowLeft,
  ArrowRight,
  CheckCircle,
  AlertCircle,
} from "lucide-react";
import "../styles/AttemptQuiz.css";
import { HashLoader } from "react-spinners";

const AttemptQuiz = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [attempt, setAttempt] = useState(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchAttempt();
  }, [id]);

  const fetchAttempt = async () => {
    try {
      const response = await api.get(`/attempts/${id}`);
      setAttempt(response.data.data || response.data);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching attempt:", error);
      setError("Failed to load quiz");
      setIsLoading(false);
    }
  };

  const handleOptionSelect = (questionId, optionId, isMultiple) => {
    setSelectedOptions((prev) => {
      const current = prev[questionId] || [];
      if (isMultiple) {
        if (current.includes(optionId)) {
          return {
            ...prev,
            [questionId]: current.filter((id) => id !== optionId),
          };
        } else {
          return { ...prev, [questionId]: [...current, optionId] };
        }
      } else {
        return { ...prev, [questionId]: [optionId] };
      }
    });
  };

  const saveAnswer = async (questionId) => {
    const selectedOptionIds = selectedOptions[questionId] || [];
    if (selectedOptionIds.length === 0) return;

    try {
      await api.post(`/attempts/${id}/answer`, {
        attempt_question_id: questionId,
        selected_option_ids: selectedOptionIds,
      });
    } catch (error) {
      console.error("Error saving answer:", error);
    }
  };

  const handleNext = async () => {
    await saveAnswer(attempt.attempt_questions[currentQuestionIndex].id);
    if (currentQuestionIndex < attempt.attempt_questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  const handlePrevious = async () => {
    await saveAnswer(attempt.attempt_questions[currentQuestionIndex].id);
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  const handleSubmit = async () => {
    await saveAnswer(attempt.attempt_questions[currentQuestionIndex].id);
    setIsSubmitting(true);
    try {
      await api.post(`/attempts/${id}/submit`);
      navigate("/student/results");
    } catch (error) {
      console.error("Error submitting quiz:", error);
      setError("Failed to submit quiz");
    }
    setIsSubmitting(false);
  };

  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
    );
  }

  if (error || !attempt) {
    return (
      <div className="attempt-quiz-container error-screen">
        <AlertCircle className="error-icon" />
        <p className="error-title">Error</p>
        <p className="error-message">{error || "Quiz not found"}</p>
      </div>
    );
  }

  const currentQuestion = attempt.attempt_questions[currentQuestionIndex];
  const isMultiple = currentQuestion.type === "multiple_choice";

  return (
    <div className="attempt-quiz-container">
      <div className="quiz-card">
        <div className="question-header">
          <div className="quiz-title">
            <BookOpen size={28} color="#1c2a45" />
            <h1>{attempt.subject?.name || "Quiz"}</h1>
          </div>
          <div className="question-progress">
            <ListOrdered size={18} />
            <span>
              Question {currentQuestionIndex + 1} of{" "}
              {attempt.attempt_questions.length}
            </span>
          </div>
          <div className="progress-bar">
            <div
              className="progress-fill"
              style={{
                width: `${
                  ((currentQuestionIndex + 1) /
                    attempt.attempt_questions.length) *
                  100
                }%`,
              }}
            ></div>
          </div>
        </div>

        <div className="question-body">
          <h2>{currentQuestion.question_text}</h2>
          {currentQuestion.direction && (
            <p
              className={`question-direction ${
                currentQuestion?.direction === "ltr" ? "ltr" : "rtl"
              }`}
            >
              {currentQuestion.direction}
            </p>
          )}

          <div className="options-list">
            {currentQuestion.attempt_options.map((option) => (
              <label key={option.id} className="option-label">
                <input
                  type={isMultiple ? "checkbox" : "radio"}
                  name={`question-${currentQuestion.id}`}
                  value={option.id}
                  checked={(selectedOptions[currentQuestion.id] || []).includes(
                    option.id
                  )}
                  onChange={() =>
                    handleOptionSelect(
                      currentQuestion.id,
                      option.id,
                      isMultiple
                    )
                  }
                />
                <span>{option.option_text}</span>
                {option.direction && (
                  <span className="option-direction">({option.direction})</span>
                )}
              </label>
            ))}
          </div>
        </div>

        <div className="navigation-buttons">
          <button
            onClick={handlePrevious}
            disabled={currentQuestionIndex === 0}
            className="btn-prev"
          >
            <ArrowLeft size={16} />
            Previous
          </button>

          {currentQuestionIndex < attempt.attempt_questions.length - 1 ? (
            <button onClick={handleNext} className="btn-next">
              Next
              <ArrowRight size={16} />
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="btn-submit"
            >
              <CheckCircle size={16} />
              {isSubmitting ? "Submitting..." : "Submit Quiz"}
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AttemptQuiz;
