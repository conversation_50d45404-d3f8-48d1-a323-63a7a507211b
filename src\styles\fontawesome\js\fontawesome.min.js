/*!
 * Font Awesome Free 7.0.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2025 Fonticons, Inc.
 */
(()=>{function L(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,a=Array(e);n<e;n++)a[n]=t[n];return a}function T(t,e){for(var n=0;n<e.length;n++){var a=e[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,Y(a.key),a)}}function R(t,e){var n,a,r,o,i,s="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(s)return r=!(a=!0),{s:function(){s=s.call(t)},n:function(){var t=s.next();return a=t.done,t},e:function(t){r=!0,n=t},f:function(){try{a||null==s.return||s.return()}finally{if(r)throw n}}};if(Array.isArray(t)||(s=H(t))||e&&t&&"number"==typeof t.length)return s&&(t=s),o=0,{s:i=function(){},n:function(){return o>=t.length?{done:!0}:{done:!1,value:t[o++]}},e:function(t){throw t},f:i};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function a(t,e,n){return(e=Y(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function W(e,t){var n,a=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,n)),a}function p(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?W(Object(n),!0).forEach(function(t){a(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):W(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function m(t,e){return(t=>{if(Array.isArray(t))return t})(t)||((t,e)=>{var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var a,r,o,i,s=[],l=!0,f=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(a=o.call(n)).done)&&(s.push(a.value),s.length!==e);l=!0);}catch(t){f=!0,r=t}finally{try{if(!l&&null!=n.return&&(i=n.return(),Object(i)!==i))return}finally{if(f)throw r}}return s}})(t,e)||H(t,e)||(()=>{throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")})()}function b(t){return(t=>{if(Array.isArray(t))return L(t)})(t)||(t=>{if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)})(t)||H(t)||(()=>{throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")})()}function Y(t){var e=((t,e)=>{if("object"!=typeof t||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);if("object"!=typeof(n=n.call(t,e||"default")))return n;throw new TypeError("@@toPrimitive must return a primitive value.")})(t,"string");return"symbol"==typeof e?e:e+""}function J(t){return(J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function H(t,e){var n;if(t)return"string"==typeof t?L(t,e):"Map"===(n="Object"===(n={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?L(t,e):void 0}function B(){}var t={},e={},n=null,r={mark:B,measure:B};try{"undefined"!=typeof window&&(t=window),"undefined"!=typeof document&&(e=document),"undefined"!=typeof MutationObserver&&(n=MutationObserver),"undefined"!=typeof performance&&(r=performance)}catch(q){}var o=void 0===(o=(t.navigator||{}).userAgent)?"":o,v=t,y=e,U=n,t=r,V=!!v.document,h=!!y.documentElement&&!!y.head&&"function"==typeof y.addEventListener&&"function"==typeof y.createElement,_=~o.indexOf("MSIE")||~o.indexOf("Trident/"),e={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fab:"brands","fa-brands":"brands"},duotone:{fa:"solid",fad:"solid","fa-solid":"solid","fa-duotone":"solid",fadr:"regular","fa-regular":"regular",fadl:"light","fa-light":"light",fadt:"thin","fa-thin":"thin"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid",fasdr:"regular","fa-regular":"regular",fasdl:"light","fa-light":"light",fasdt:"thin","fa-thin":"thin"},slab:{"fa-regular":"regular",faslr:"regular"},"slab-press":{"fa-regular":"regular",faslpr:"regular"},thumbprint:{"fa-light":"light",fatl:"light"},whiteboard:{"fa-semibold":"semibold",fawsb:"semibold"},notdog:{"fa-solid":"solid",fans:"solid"},"notdog-duo":{"fa-solid":"solid",fands:"solid"},etch:{"fa-solid":"solid",faes:"solid"},jelly:{"fa-regular":"regular",fajr:"regular"},"jelly-fill":{"fa-regular":"regular",fajfr:"regular"},"jelly-duo":{"fa-regular":"regular",fajdr:"regular"},chisel:{"fa-regular":"regular",facr:"regular"}},X=["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-thumbprint","fa-whiteboard","fa-notdog","fa-notdog-duo","fa-chisel","fa-etch","fa-jelly","fa-jelly-fill","fa-jelly-duo","fa-slab","fa-slab-press"],g="classic",x="duotone",q="thumbprint",K=[g,x,n="sharp",r="sharp-duotone",o="chisel",Q="etch",i="jelly",s="jelly-duo",l="jelly-fill",f="notdog",k="notdog-duo","slab",et="slab-press",q,nt="whiteboard"],G=(a(a(a(a(a(a(a(a(a(a(c={},g,"Classic"),x,"Duotone"),n,"Sharp"),r,"Sharp Duotone"),o,"Chisel"),Q,"Etch"),i,"Jelly"),s,"Jelly Duo"),l,"Jelly Fill"),f,"Notdog"),a(a(a(a(a(c,k,"Notdog Duo"),"slab","Slab"),et,"Slab Press"),q,"Thumbprint"),nt,"Whiteboard"),new Map([["classic",{defaultShortPrefixId:"fas",defaultStyleId:"solid",styleIds:["solid","regular","light","thin","brands"],futureStyleIds:[],defaultFontWeight:900}],["duotone",{defaultShortPrefixId:"fad",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp",{defaultShortPrefixId:"fass",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["sharp-duotone",{defaultShortPrefixId:"fasds",defaultStyleId:"solid",styleIds:["solid","regular","light","thin"],futureStyleIds:[],defaultFontWeight:900}],["chisel",{defaultShortPrefixId:"facr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["etch",{defaultShortPrefixId:"faes",defaultStyleId:"solid",styleIds:["solid"],futureStyleIds:[],defaultFontWeight:900}],["jelly",{defaultShortPrefixId:"fajr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["jelly-duo",{defaultShortPrefixId:"fajdr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["jelly-fill",{defaultShortPrefixId:"fajfr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["notdog",{defaultShortPrefixId:"fans",defaultStyleId:"solid",styleIds:["solid"],futureStyleIds:[],defaultFontWeight:900}],["notdog-duo",{defaultShortPrefixId:"fands",defaultStyleId:"solid",styleIds:["solid"],futureStyleIds:[],defaultFontWeight:900}],["slab",{defaultShortPrefixId:"faslr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["slab-press",{defaultShortPrefixId:"faslpr",defaultStyleId:"regular",styleIds:["regular"],futureStyleIds:[],defaultFontWeight:400}],["thumbprint",{defaultShortPrefixId:"fatl",defaultStyleId:"light",styleIds:["light"],futureStyleIds:[],defaultFontWeight:300}],["whiteboard",{defaultShortPrefixId:"fawsb",defaultStyleId:"semibold",styleIds:["semibold"],futureStyleIds:[],defaultFontWeight:600}]])),$=["fak","fa-kit","fakd","fa-kit-duotone"],n={fak:"kit","fa-kit":"kit"},r={fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"},o=(a(a({},"kit","Kit"),"kit-duotone","Kit Duotone"),{kit:"fak"}),Q={"kit-duotone":"fakd"},i="duotone-group",s="swap-opacity",l="primary",f="secondary",Z=(a(a(a(a(a(a(a(a(a(a(c={},"classic","Classic"),"duotone","Duotone"),"sharp","Sharp"),"sharp-duotone","Sharp Duotone"),"chisel","Chisel"),"etch","Etch"),"jelly","Jelly"),"jelly-duo","Jelly Duo"),"jelly-fill","Jelly Fill"),"notdog","Notdog"),a(a(a(a(a(c,"notdog-duo","Notdog Duo"),"slab","Slab"),"slab-press","Slab Press"),"thumbprint","Thumbprint"),"whiteboard","Whiteboard"),a(a({},"kit","Kit"),"kit-duotone","Kit Duotone"),{classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},duotone:{fadr:"fa-regular",fadl:"fa-light",fadt:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid",fasdr:"fa-regular",fasdl:"fa-light",fasdt:"fa-thin"},slab:{faslr:"fa-regular"},"slab-press":{faslpr:"fa-regular"},whiteboard:{fawsb:"fa-semibold"},thumbprint:{fatl:"fa-light"},notdog:{fans:"fa-solid"},"notdog-duo":{fands:"fa-solid"},etch:{faes:"fa-solid"},jelly:{fajr:"fa-regular"},"jelly-fill":{fajfr:"fa-regular"},"jelly-duo":{fajdr:"fa-regular"},chisel:{facr:"fa-regular"}}),tt=["fa","fas","far","fal","fat","fad","fadr","fadl","fadt","fab","fass","fasr","fasl","fast","fasds","fasdr","fasdl","fasdt","faslr","faslpr","fawsb","fatl","fans","fands","faes","fajr","fajfr","fajdr","facr"].concat(["fa-classic","fa-duotone","fa-sharp","fa-sharp-duotone","fa-thumbprint","fa-whiteboard","fa-notdog","fa-notdog-duo","fa-chisel","fa-etch","fa-jelly","fa-jelly-fill","fa-jelly-duo","fa-slab","fa-slab-press"],["fa-solid","fa-regular","fa-light","fa-thin","fa-duotone","fa-brands","fa-semibold"]),et=(k=[1,2,3,4,5,6,7,8,9,10]).concat([11,12,13,14,15,16,17,18,19,20]),nt=[].concat(b(Object.keys({classic:["fas","far","fal","fat","fad"],duotone:["fadr","fadl","fadt"],sharp:["fass","fasr","fasl","fast"],"sharp-duotone":["fasds","fasdr","fasdl","fasdt"],slab:["faslr"],"slab-press":["faslpr"],whiteboard:["fawsb"],thumbprint:["fatl"],notdog:["fans"],"notdog-duo":["fands"],etch:["faes"],jelly:["fajr"],"jelly-fill":["fajfr"],"jelly-duo":["fajdr"],chisel:["facr"]})),["solid","regular","light","thin","duotone","brands","semibold"],["aw","fw","pull-left","pull-right"],["2xs","xs","sm","lg","xl","2xl","beat","border","fade","beat-fade","bounce","flip-both","flip-horizontal","flip-vertical","flip","inverse","layers","layers-bottom-left","layers-bottom-right","layers-counter","layers-text","layers-top-left","layers-top-right","li","pull-end","pull-start","pulse","rotate-180","rotate-270","rotate-90","rotate-by","shake","spin-pulse","spin-reverse","spin","stack-1x","stack-2x","stack","ul","width-auto","width-fixed",i,s,l,f]).concat(k.map(function(t){return"".concat(t,"x")})).concat(et.map(function(t){return"w-".concat(t)})),c="___FONT_AWESOME___",at=16,rt="svg-inline--fa",w="data-fa-i2svg",ot="data-fa-pseudo-element",it="data-fa-pseudo-element-pending",st="data-prefix",lt="data-icon",ft="fontawesome-i2svg",ct="async",ut=["HTML","HEAD","STYLE","SCRIPT"],dt=["::before","::after",":before",":after"],mt=(()=>{try{return!0}catch(t){return!1}})();function u(t){return new Proxy(t,{get:function(t,e){return e in t?t[e]:t[g]}})}(i=p({},e))[g]=p(p(p(p({},{"fa-duotone":"duotone"}),e[g]),n),r);var ht=u(i),gt=((s=p({},{chisel:{regular:"facr"},classic:{brands:"fab",light:"fal",regular:"far",solid:"fas",thin:"fat"},duotone:{light:"fadl",regular:"fadr",solid:"fad",thin:"fadt"},etch:{solid:"faes"},jelly:{regular:"fajr"},"jelly-duo":{regular:"fajdr"},"jelly-fill":{regular:"fajfr"},notdog:{solid:"fans"},"notdog-duo":{solid:"fands"},sharp:{light:"fasl",regular:"fasr",solid:"fass",thin:"fast"},"sharp-duotone":{light:"fasdl",regular:"fasdr",solid:"fasds",thin:"fasdt"},slab:{regular:"faslr"},"slab-press":{regular:"faslpr"},thumbprint:{light:"fatl"},whiteboard:{semibold:"fawsb"}}))[g]=p(p(p(p({},{duotone:"fad"}),s[g]),o),Q),u(s)),pt=((l=p({},Z))[g]=p(p({},l[g]),{fak:"fa-kit"}),u(l)),bt=((f=p({},{classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},duotone:{"fa-regular":"fadr","fa-light":"fadl","fa-thin":"fadt"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds","fa-regular":"fasdr","fa-light":"fasdl","fa-thin":"fasdt"},slab:{"fa-regular":"faslr"},"slab-press":{"fa-regular":"faslpr"},whiteboard:{"fa-semibold":"fawsb"},thumbprint:{"fa-light":"fatl"},notdog:{"fa-solid":"fans"},"notdog-duo":{"fa-solid":"fands"},etch:{"fa-solid":"faes"},jelly:{"fa-regular":"fajr"},"jelly-fill":{"fa-regular":"fajfr"},"jelly-duo":{"fa-regular":"fajdr"},chisel:{"fa-regular":"facr"}}))[g]=p(p({},f[g]),{"fa-kit":"fak"}),u(f),/fa(k|kd|s|r|l|t|d|dr|dl|dt|b|slr|slpr|wsb|tl|ns|nds|es|jr|jfr|jdr|cr|ss|sr|sl|st|sds|sdr|sdl|sdt)?[\-\ ]/),vt="fa-layers-text",yt=/Font ?Awesome ?([567 ]*)(Solid|Regular|Light|Thin|Duotone|Brands|Free|Pro|Sharp Duotone|Sharp|Kit|Notdog Duo|Notdog|Chisel|Etch|Thumbprint|Jelly Fill|Jelly Duo|Jelly|Slab Press|Slab|Whiteboard)?.*/i,xt=(u(p({},{classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},duotone:{900:"fad",400:"fadr",300:"fadl",100:"fadt"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds",400:"fasdr",300:"fasdl",100:"fasdt"},slab:{400:"faslr"},"slab-press":{400:"faslpr"},whiteboard:{600:"fawsb"},thumbprint:{300:"fatl"},notdog:{900:"fans"},"notdog-duo":{900:"fands"},etch:{900:"faes"},chisel:{400:"facr"},jelly:{400:"fajr"},"jelly-fill":{400:"fajfr"},"jelly-duo":{400:"fajdr"}})),["class","data-prefix","data-icon","data-fa-transform","data-fa-mask"]),wt={GROUP:"duotone-group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},kt=[].concat(b(["kit"]),b(nt)),d=v.FontAwesomeConfig||{},k=(y&&"function"==typeof y.querySelector&&[["data-family-prefix","familyPrefix"],["data-css-prefix","cssPrefix"],["data-family-default","familyDefault"],["data-style-default","styleDefault"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-search-pseudo-elements","searchPseudoElements"],["data-search-pseudo-elements-warnings","searchPseudoElementsWarnings"],["data-search-pseudo-elements-full-scan","searchPseudoElementsFullScan"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach(function(t){var e=m(t,2),n=e[0],e=e[1],n=""===(t=(t=>{var e=y.querySelector("script["+t+"]");if(e)return e.getAttribute(t)})(n))||"false"!==t&&("true"===t||t);null!=n&&(d[e]=n)}),{styleDefault:"solid",familyDefault:g,cssPrefix:"fa",replacementClass:rt,autoReplaceSvg:!0,autoAddCss:!0,searchPseudoElements:!1,searchPseudoElementsWarnings:!0,searchPseudoElementsFullScan:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0}),S=(d.familyPrefix&&(d.cssPrefix=d.familyPrefix),p(p({},k),d)),A=(S.autoReplaceSvg||(S.observeMutations=!1),{}),St=(Object.keys(k).forEach(function(e){Object.defineProperty(A,e,{enumerable:!0,set:function(t){S[e]=t,St.forEach(function(t){return t(A)})},get:function(){return S[e]}})}),Object.defineProperty(A,"familyPrefix",{enumerable:!0,set:function(t){S.cssPrefix=t,St.forEach(function(t){return t(A)})},get:function(){return S.cssPrefix}}),v.FontAwesomeConfig=A,[]),j=at,P={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};function At(){for(var t=12,e="";0<t--;)e+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"[62*Math.random()|0];return e}function F(t){for(var e=[],n=(t||[]).length>>>0;n--;)e[n]=t[n];return e}function jt(t){return t.classList?F(t.classList):(t.getAttribute("class")||"").split(" ").filter(function(t){return t})}function Pt(t){return"".concat(t).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function Ft(n){return Object.keys(n||{}).reduce(function(t,e){return t+"".concat(e,": ").concat(n[e].trim(),";")},"")}function It(t){return t.size!==P.size||t.x!==P.x||t.y!==P.y||t.rotate!==P.rotate||t.flipX||t.flipY}function Nt(){var t,e,n=rt,a=A.cssPrefix,r=A.replacementClass,o=':root, :host {\n  --fa-font-solid: normal 900 1em/1 "Font Awesome 7 Free";\n  --fa-font-regular: normal 400 1em/1 "Font Awesome 7 Free";\n  --fa-font-light: normal 300 1em/1 "Font Awesome 7 Pro";\n  --fa-font-thin: normal 100 1em/1 "Font Awesome 7 Pro";\n  --fa-font-duotone: normal 900 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-duotone-regular: normal 400 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-duotone-light: normal 300 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-duotone-thin: normal 100 1em/1 "Font Awesome 7 Duotone";\n  --fa-font-brands: normal 400 1em/1 "Font Awesome 7 Brands";\n  --fa-font-sharp-solid: normal 900 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-regular: normal 400 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-light: normal 300 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-thin: normal 100 1em/1 "Font Awesome 7 Sharp";\n  --fa-font-sharp-duotone-solid: normal 900 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-sharp-duotone-regular: normal 400 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-sharp-duotone-light: normal 300 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-sharp-duotone-thin: normal 100 1em/1 "Font Awesome 7 Sharp Duotone";\n  --fa-font-slab-regular: normal 400 1em/1 "Font Awesome 7 Slab";\n  --fa-font-slab-press-regular: normal 400 1em/1 "Font Awesome 7 Slab Press";\n  --fa-font-whiteboard-semibold: normal 600 1em/1 "Font Awesome 7 Whiteboard";\n  --fa-font-thumbprint-light: normal 300 1em/1 "Font Awesome 7 Thumbprint";\n  --fa-font-notdog-solid: normal 900 1em/1 "Font Awesome 7 Notdog";\n  --fa-font-notdog-duo-solid: normal 900 1em/1 "Font Awesome 7 Notdog Duo";\n  --fa-font-etch-solid: normal 900 1em/1 "Font Awesome 7 Etch";\n  --fa-font-jelly-regular: normal 400 1em/1 "Font Awesome 7 Jelly";\n  --fa-font-jelly-fill-regular: normal 400 1em/1 "Font Awesome 7 Jelly Fill";\n  --fa-font-jelly-duo-regular: normal 400 1em/1 "Font Awesome 7 Jelly Duo";\n  --fa-font-chisel-regular: normal 400 1em/1 "Font Awesome 7 Chisel";\n}\n\n.svg-inline--fa {\n  box-sizing: content-box;\n  display: var(--fa-display, inline-block);\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n  width: var(--fa-width, 1.25em);\n}\n.svg-inline--fa.fa-2xs {\n  vertical-align: 0.1em;\n}\n.svg-inline--fa.fa-xs {\n  vertical-align: 0em;\n}\n.svg-inline--fa.fa-sm {\n  vertical-align: -0.0714285714em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.2em;\n}\n.svg-inline--fa.fa-xl {\n  vertical-align: -0.25em;\n}\n.svg-inline--fa.fa-2xl {\n  vertical-align: -0.3125em;\n}\n.svg-inline--fa.fa-pull-left,\n.svg-inline--fa .fa-pull-start {\n  float: inline-start;\n  margin-inline-end: var(--fa-pull-margin, 0.3em);\n}\n.svg-inline--fa.fa-pull-right,\n.svg-inline--fa .fa-pull-end {\n  float: inline-end;\n  margin-inline-start: var(--fa-pull-margin, 0.3em);\n}\n.svg-inline--fa.fa-li {\n  width: var(--fa-li-width, 2em);\n  inset-inline-start: calc(-1 * var(--fa-li-width, 2em));\n  inset-block-start: 0.25em; /* syncing vertical alignment with Web Font rendering */\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: var(--fa-width, 1.25em);\n}\n.fa-layers .svg-inline--fa {\n  inset: 0;\n  margin: auto;\n  position: absolute;\n  transform-origin: center center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: var(--fa-counter-background-color, #ff253a);\n  border-radius: var(--fa-counter-border-radius, 1em);\n  box-sizing: border-box;\n  color: var(--fa-inverse, #fff);\n  line-height: var(--fa-counter-line-height, 1);\n  max-width: var(--fa-counter-max-width, 5em);\n  min-width: var(--fa-counter-min-width, 1.5em);\n  overflow: hidden;\n  padding: var(--fa-counter-padding, 0.25em 0.5em);\n  right: var(--fa-right, 0);\n  text-overflow: ellipsis;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-counter-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: var(--fa-bottom, 0);\n  right: var(--fa-right, 0);\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: var(--fa-bottom, 0);\n  left: var(--fa-left, 0);\n  right: auto;\n  top: auto;\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  top: var(--fa-top, 0);\n  right: var(--fa-right, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: var(--fa-left, 0);\n  right: auto;\n  top: var(--fa-top, 0);\n  transform: scale(var(--fa-layers-scale, 0.25));\n  transform-origin: top left;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-2xs {\n  font-size: calc(10 / 16 * 1em); /* converts a 10px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 10 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 10 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-xs {\n  font-size: calc(12 / 16 * 1em); /* converts a 12px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 12 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 12 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-sm {\n  font-size: calc(14 / 16 * 1em); /* converts a 14px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 14 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 14 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-lg {\n  font-size: calc(20 / 16 * 1em); /* converts a 20px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 20 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 20 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-xl {\n  font-size: calc(24 / 16 * 1em); /* converts a 24px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 24 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 24 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-2xl {\n  font-size: calc(32 / 16 * 1em); /* converts a 32px size into an em-based value that\'s relative to the scale\'s 16px base */\n  line-height: calc(1 / 32 * 1em); /* sets the line-height of the icon back to that of it\'s parent */\n  vertical-align: calc((6 / 32 - 0.375) * 1em); /* vertically centers the icon taking into account the surrounding text\'s descender */\n}\n\n.fa-width-auto {\n  --fa-width: auto;\n}\n\n.fa-fw,\n.fa-width-fixed {\n  --fa-width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-inline-start: var(--fa-li-margin, 2.5em);\n  padding-inline-start: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  inset-inline-start: calc(-1 * var(--fa-li-width, 2em));\n  position: absolute;\n  text-align: center;\n  width: var(--fa-li-width, 2em);\n  line-height: inherit;\n}\n\n/* Heads Up: Bordered Icons will not be supported in the future!\n  - This feature will be deprecated in the next major release of Font Awesome (v8)!\n  - You may continue to use it in this version *v7), but it will not be supported in Font Awesome v8.\n*/\n/* Notes:\n* --@{v.$css-prefix}-border-width = 1/16 by default (to render as ~1px based on a 16px default font-size)\n* --@{v.$css-prefix}-border-padding =\n  ** 3/16 for vertical padding (to give ~2px of vertical whitespace around an icon considering it\'s vertical alignment)\n  ** 4/16 for horizontal padding (to give ~4px of horizontal whitespace around an icon)\n*/\n.fa-border {\n  border-color: var(--fa-border-color, #eee);\n  border-radius: var(--fa-border-radius, 0.1em);\n  border-style: var(--fa-border-style, solid);\n  border-width: var(--fa-border-width, 0.0625em);\n  box-sizing: var(--fa-border-box-sizing, content-box);\n  padding: var(--fa-border-padding, 0.1875em 0.25em);\n}\n\n.fa-pull-left,\n.fa-pull-start {\n  float: inline-start;\n  margin-inline-end: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-pull-right,\n.fa-pull-end {\n  float: inline-end;\n  margin-inline-start: var(--fa-pull-margin, 0.3em);\n}\n\n.fa-beat {\n  animation-name: fa-beat;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-bounce {\n  animation-name: fa-bounce;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));\n}\n\n.fa-fade {\n  animation-name: fa-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-beat-fade {\n  animation-name: fa-beat-fade;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));\n}\n\n.fa-flip {\n  animation-name: fa-flip;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, ease-in-out);\n}\n\n.fa-shake {\n  animation-name: fa-shake;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin {\n  animation-name: fa-spin;\n  animation-delay: var(--fa-animation-delay, 0s);\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 2s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, linear);\n}\n\n.fa-spin-reverse {\n  --fa-animation-direction: reverse;\n}\n\n.fa-pulse,\n.fa-spin-pulse {\n  animation-name: fa-spin;\n  animation-direction: var(--fa-animation-direction, normal);\n  animation-duration: var(--fa-animation-duration, 1s);\n  animation-iteration-count: var(--fa-animation-iteration-count, infinite);\n  animation-timing-function: var(--fa-animation-timing, steps(8));\n}\n\n@media (prefers-reduced-motion: reduce) {\n  .fa-beat,\n  .fa-bounce,\n  .fa-fade,\n  .fa-beat-fade,\n  .fa-flip,\n  .fa-pulse,\n  .fa-shake,\n  .fa-spin,\n  .fa-spin-pulse {\n    animation: none !important;\n    transition: none !important;\n  }\n}\n@keyframes fa-beat {\n  0%, 90% {\n    transform: scale(1);\n  }\n  45% {\n    transform: scale(var(--fa-beat-scale, 1.25));\n  }\n}\n@keyframes fa-bounce {\n  0% {\n    transform: scale(1, 1) translateY(0);\n  }\n  10% {\n    transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);\n  }\n  30% {\n    transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));\n  }\n  50% {\n    transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);\n  }\n  57% {\n    transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));\n  }\n  64% {\n    transform: scale(1, 1) translateY(0);\n  }\n  100% {\n    transform: scale(1, 1) translateY(0);\n  }\n}\n@keyframes fa-fade {\n  50% {\n    opacity: var(--fa-fade-opacity, 0.4);\n  }\n}\n@keyframes fa-beat-fade {\n  0%, 100% {\n    opacity: var(--fa-beat-fade-opacity, 0.4);\n    transform: scale(1);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(var(--fa-beat-fade-scale, 1.125));\n  }\n}\n@keyframes fa-flip {\n  50% {\n    transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));\n  }\n}\n@keyframes fa-shake {\n  0% {\n    transform: rotate(-15deg);\n  }\n  4% {\n    transform: rotate(15deg);\n  }\n  8%, 24% {\n    transform: rotate(-18deg);\n  }\n  12%, 28% {\n    transform: rotate(18deg);\n  }\n  16% {\n    transform: rotate(-22deg);\n  }\n  20% {\n    transform: rotate(22deg);\n  }\n  32% {\n    transform: rotate(-12deg);\n  }\n  36% {\n    transform: rotate(12deg);\n  }\n  40%, 100% {\n    transform: rotate(0deg);\n  }\n}\n@keyframes fa-spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  transform: scale(1, -1);\n}\n\n.fa-flip-both,\n.fa-flip-horizontal.fa-flip-vertical {\n  transform: scale(-1, -1);\n}\n\n.fa-rotate-by {\n  transform: rotate(var(--fa-rotate-angle, 0));\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}\n\n.svg-inline--fa.fa-inverse {\n  fill: var(--fa-inverse, #fff);\n}\n\n.fa-stack {\n  display: inline-block;\n  height: 2em;\n  line-height: 2em;\n  position: relative;\n  vertical-align: middle;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: var(--fa-inverse, #fff);\n}\n\n.svg-inline--fa.fa-stack-1x {\n  --fa-width: 1.25em;\n  height: 1em;\n  width: var(--fa-width);\n}\n.svg-inline--fa.fa-stack-2x {\n  --fa-width: 2.5em;\n  height: 2em;\n  width: var(--fa-width);\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  inset: 0;\n  margin: auto;\n  position: absolute;\n  z-index: var(--fa-stack-z-index, auto);\n}';return"fa"===a&&r===n||(t=new RegExp("\\.".concat("fa","\\-"),"g"),e=new RegExp("\\--".concat("fa","\\-"),"g"),n=new RegExp("\\.".concat(n),"g"),o=o.replace(t,".".concat(a,"-")).replace(e,"--".concat(a,"-")).replace(n,".".concat(r))),o}var Ot=!1;function Et(){if(A.autoAddCss&&!Ot){var t=Nt();if(t&&h){for(var e=y.createElement("style"),n=(e.setAttribute("type","text/css"),e.innerHTML=t,y.head.childNodes),a=null,r=n.length-1;-1<r;r--){var o=n[r],i=(o.tagName||"").toUpperCase();-1<["STYLE","LINK"].indexOf(i)&&(a=o)}y.head.insertBefore(e,a)}Ot=!0}}function Ct(){y.removeEventListener("DOMContentLoaded",Ct),Mt=1,zt.map(function(t){return t()})}var et={mixout:function(){return{dom:{css:Nt,insertCss:Et}}},hooks:function(){return{beforeDOMElementCreation:function(){Et()},beforeI2svg:function(){Et()}}}},I=((e=v||{})[c]||(e[c]={}),e[c].styles||(e[c].styles={}),e[c].hooks||(e[c].hooks={}),e[c].shims||(e[c].shims=[]),e[c]),zt=[],Mt=!1;function Dt(t){h&&(Mt?setTimeout(t,0):zt.push(t))}function N(t){var n,e=t.tag,a=t.attributes,a=void 0===a?{}:a,r=t.children,r=void 0===r?[]:r;return"string"==typeof t?Pt(t):"<".concat(e," ").concat((n=a,Object.keys(n||{}).reduce(function(t,e){return t+"".concat(e,'="').concat(Pt(n[e]),'" ')},"").trim()),">").concat(r.map(N).join(""),"</").concat(e,">")}function Lt(t,e,n){if(t&&t[e]&&t[e][n])return{prefix:e,iconName:n,icon:t[e][n]}}function Tt(t,e,n,a){for(var r,o,i=Object.keys(t),s=i.length,l=void 0!==a?Rt(e,a):e,f=void 0===n?(r=1,t[i[0]]):(r=0,n);r<s;r++)f=l(f,t[o=i[r]],o,t);return f}h&&!(Mt=(y.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(y.readyState))&&y.addEventListener("DOMContentLoaded",Ct);var Rt=function(r,o){return function(t,e,n,a){return r.call(o,t,e,n,a)}};function Wt(t){return 1!==b(t).length?null:t.codePointAt(0).toString(16)}function Yt(a){return Object.keys(a).reduce(function(t,e){var n=a[e];return!!n.icon?t[n.iconName]=n.icon:t[e]=n,t},{})}function Jt(t,e,n){var a=(2<arguments.length&&void 0!==n?n:{}).skipHooks,a=void 0!==a&&a,r=Yt(e);"function"!=typeof I.hooks.addPack||a?I.styles[t]=p(p({},I.styles[t]||{}),r):I.hooks.addPack(t,Yt(e)),"fas"===t&&Jt("fa",e)}var Ht,Bt=I.styles,Ut=I.shims,Vt=Object.keys(pt),_t=Vt.reduce(function(t,e){return t[e]=Object.keys(pt[e]),t},{}),O=null,Xt={},qt={},Kt={},Gt={},$t={};function Qt(t,e){var n=e.split("-"),a=n[0],n=n.slice(1).join("-");return a!==t||""===n||~kt.indexOf(n)?null:n}function Zt(){function t(a){return Tt(Bt,function(t,e,n){return t[n]=Tt(e,a,{}),t},{})}Xt=t(function(e,t,n){return t[3]&&(e[t[3]]=n),t[2]&&t[2].filter(function(t){return"number"==typeof t}).forEach(function(t){e[t.toString(16)]=n}),e}),qt=t(function(e,t,n){return e[n]=n,t[2]&&t[2].filter(function(t){return"string"==typeof t}).forEach(function(t){e[t]=n}),e}),$t=t(function(e,t,n){var a=t[2];return e[n]=n,a.forEach(function(t){e[t]=n}),e});var o="far"in Bt||A.autoFetchSvg,e=Tt(Ut,function(t,e){var n=e[0],a=e[1],r=e[2];return"far"!==a||o||(a="fas"),"string"==typeof n&&(t.names[n]={prefix:a,iconName:r}),"number"==typeof n&&(t.unicodes[n.toString(16)]={prefix:a,iconName:r}),t},{names:{},unicodes:{}});Kt=e.names,Gt=e.unicodes,O=ae(A.styleDefault,{family:A.familyDefault})}function te(t,e){return(Xt[t]||{})[e]}function E(t,e){return($t[t]||{})[e]}function ee(t){return Kt[t]||{prefix:null,iconName:null}}Ht=function(t){O=ae(t.styleDefault,{family:A.familyDefault})},St.push(Ht),Zt();var ne=function(){return{prefix:null,iconName:null,rest:[]}};function ae(t,e){var n=(1<arguments.length&&void 0!==e?e:{}).family,n=void 0===n?g:n,a=ht[n][t];return n!==x||t?(n=gt[n][t]||gt[n][a],a=t in I.styles?t:null,n||a||null):"fad"}function re(t){return t.sort().filter(function(t,e,n){return n.indexOf(t)===e})}var oe=tt.concat($);function ie(t,e){var n,a,r,o,i,s,l=(1<arguments.length&&void 0!==e?e:{}).skipLookups,l=void 0!==l&&l,f=null,c=re(t.filter(function(t){return oe.includes(t)})),u=re(t.filter(function(t){return!oe.includes(t)})),d=m(c.filter(function(t){return f=t,!X.includes(t)}),1)[0],d=void 0===d?null:d,c=(n=c,a=g,r=Vt.reduce(function(t,e){return t[e]="".concat(A.cssPrefix,"-").concat(e),t},{}),K.forEach(function(e){(n.includes(r[e])||n.some(function(t){return _t[e].includes(t)}))&&(a=e)}),a),u=p(p({},(o=[],i=null,u.forEach(function(t){var e=Qt(A.cssPrefix,t);e?i=e:t&&o.push(t)}),{iconName:i,rest:o})),{},{prefix:ae(d,{family:c})});return p(p(p({},u),(t=>{var e=t.values,n=t.family,a=t.canonical,r=void 0===(r=t.givenPrefix)?"":r,o=void 0===(o=t.styles)?{}:o,i=void 0===(i=t.config)?{}:i,s=n===x,l=e.includes("fa-duotone")||e.includes("fad"),f="duotone"===i.familyDefault,c="fad"===a.prefix||"fa-duotone"===a.prefix;return!s&&(l||f||c)&&(a.prefix="fad"),(e.includes("fa-brands")||e.includes("fab"))&&(a.prefix="fab"),!a.prefix&&se.includes(n)&&(Object.keys(o).find(function(t){return le.includes(t)})||i.autoFetchSvg)&&(s=G.get(n).defaultShortPrefixId,a.prefix=s,a.iconName=E(a.prefix,a.iconName)||a.iconName),"fa"!==a.prefix&&"fa"!==r||(a.prefix=O||"fas"),a})({values:t,family:c,styles:Bt,config:A,canonical:u,givenPrefix:f})),(e=l,t=f,d=(s=u).prefix,c=s.iconName,!e&&d&&c&&(l="fa"===t?ee(c):{},u=E(d,c),c=l.iconName||u||c,"far"!==(d=l.prefix||d)||Bt.far||!Bt.fas||A.autoFetchSvg||(d="fas")),{prefix:d,iconName:c}))}var se=K.filter(function(t){return t!==g||t!==x}),le=Object.keys(Z).filter(function(t){return t!==g}).map(function(t){return Object.keys(Z[t])}).flat(),n=(()=>{function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.definitions={}}return e=t,(n=[{key:"add",value:function(){for(var n=this,t=arguments.length,e=new Array(t),a=0;a<t;a++)e[a]=arguments[a];var r=e.reduce(this._pullDefinitions,{});Object.keys(r).forEach(function(t){n.definitions[t]=p(p({},n.definitions[t]||{}),r[t]),Jt(t,r[t]);var e=pt[g][t];e&&Jt(e,r[t]),Zt()})}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(o,t){var i=t.prefix&&t.iconName&&t.icon?{0:t}:t;return Object.keys(i).map(function(t){var e=i[t],n=e.prefix,a=e.iconName,r=e.icon,e=r[2];o[n]||(o[n]={}),0<e.length&&e.forEach(function(t){"string"==typeof t&&(o[n][t]=r)}),o[n][a]=r}),o}}])&&T(e.prototype,n),a&&T(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e;var e,n,a})(),r=[],C={},z={},fe=Object.keys(z);function ce(t,e){for(var n=arguments.length,a=new Array(2<n?n-2:0),r=2;r<n;r++)a[r-2]=arguments[r];return(C[t]||[]).forEach(function(t){e=t.apply(null,[e].concat(a))}),e}function M(t){for(var e=arguments.length,n=new Array(1<e?e-1:0),a=1;a<e;a++)n[a-1]=arguments[a];(C[t]||[]).forEach(function(t){t.apply(null,n)})}function D(t){var e=t,n=Array.prototype.slice.call(arguments,1);return z[e]?z[e].apply(null,n):void 0}function ue(t){"fa"===t.prefix&&(t.prefix="fas");var e=t.iconName,n=t.prefix||O;if(e)return e=E(n,e)||e,Lt(de.definitions,n,e)||Lt(I.styles,n,e)}var de=new n,me={noAuto:function(){A.autoReplaceSvg=!1,A.observeMutations=!1,M("noAuto")},config:A,dom:{i2svg:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return h?(M("beforeI2svg",t),D("pseudoElements2svg",t),D("i2svg",t)):Promise.reject(new Error("Operation requires a DOM of some kind."))},watch:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},e=t.autoReplaceSvgRoot;!1===A.autoReplaceSvg&&(A.autoReplaceSvg=!0),A.observeMutations=!0,Dt(function(){he({autoReplaceSvgRoot:e}),M("watch",t)})}},parse:{icon:function(t){var e,n;return null===t?null:"object"===J(t)&&t.prefix&&t.iconName?{prefix:t.prefix,iconName:E(t.prefix,t.iconName)||t.iconName}:Array.isArray(t)&&2===t.length?(e=0===t[1].indexOf("fa-")?t[1].slice(3):t[1],{prefix:n=ae(t[0]),iconName:E(n,e)||e}):"string"==typeof t&&(-1<t.indexOf("".concat(A.cssPrefix,"-"))||t.match(bt))?{prefix:(n=ie(t.split(" "),{skipLookups:!0})).prefix||O,iconName:E(n.prefix,n.iconName)||n.iconName}:"string"==typeof t?{prefix:O,iconName:E(O,t)||t}:void 0}},library:de,findIconDefinition:ue,toHtml:N},he=function(){var t=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).autoReplaceSvgRoot,t=void 0===t?y:t;(0<Object.keys(I.styles).length||A.autoFetchSvg)&&h&&A.autoReplaceSvg&&me.dom.i2svg({node:t})};function ge(e,t){return Object.defineProperty(e,"abstract",{get:t}),Object.defineProperty(e,"html",{get:function(){return e.abstract.map(N)}}),Object.defineProperty(e,"node",{get:function(){var t;if(h)return(t=y.createElement("div")).innerHTML=e.html,t.children}}),e}function pe(t){var e,n=t.icons,a=n.main,n=n.mask,r=t.prefix,o=t.iconName,i=t.transform,s=t.symbol,l=t.maskId,f=t.extra,c=t.watchable,c=void 0!==c&&c,u=n.found?n:a,d=u.width,u=u.height,m=[A.replacementClass,o?"".concat(A.cssPrefix,"-").concat(o):""].filter(function(t){return-1===f.classes.indexOf(t)}).filter(function(t){return""!==t||!!t}).concat(f.classes).join(" "),m={children:[],attributes:p(p({},f.attributes),{},{"data-prefix":r,"data-icon":o,class:m,role:f.attributes.role||"img",viewBox:"0 0 ".concat(d," ").concat(u)})},d=(e=f.attributes,["aria-label","aria-labelledby","title","role"].some(function(t){return t in e})||f.attributes["aria-hidden"]||(m.attributes["aria-hidden"]="true"),c&&(m.attributes[w]=""),p(p({},m),{},{prefix:r,iconName:o,main:a,mask:n,maskId:l,transform:i,symbol:s,styles:p({},f.styles)})),u=n.found&&a.found?D("generateAbstractMask",d)||{children:[],attributes:{}}:D("generateAbstractIcon",d)||{children:[],attributes:{}},c=u.children,m=u.attributes;return d.children=c,d.attributes=m,s?(r=d.prefix,o=d.iconName,l=d.children,i=d.attributes,r=!0===(n=d.symbol)?"".concat(r,"-").concat(A.cssPrefix,"-").concat(o):n,[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:p(p({},i),{},{id:r}),children:l}]}]):(a=(t=d).children,u=t.main,c=t.attributes,It(m=t.transform)&&u.found&&!t.mask.found&&(u=u.width/u.height/2,s=.5,c.style=Ft(p(p({},t.styles),{},{"transform-origin":"".concat(u+m.x/16,"em ").concat(s+m.y/16,"em")}))),[{tag:"svg",attributes:c,children:a}])}function be(t){var e,n=t.content,a=t.width,r=t.height,o=t.transform,i=t.extra,s=t.watchable,s=void 0!==s&&s,l=p(p({},i.attributes),{},{class:i.classes.join(" ")}),s=(s&&(l[w]=""),p({},i.styles)),o=(It(o)&&(s.transform=(i=(t={transform:o,startCentered:!0,width:a,height:r}).transform,o=t.width,a=void 0===(a=t.height)?at:a,r="",r=(r=(r+=(e=void 0!==(e=t.startCentered)&&e)&&_?"translate(".concat(i.x/j-(void 0===o?at:o)/2,"em, ").concat(i.y/j-a/2,"em) "):e?"translate(calc(-50% + ".concat(i.x/j,"em), calc(-50% + ").concat(i.y/j,"em)) "):"translate(".concat(i.x/j,"em, ").concat(i.y/j,"em) "))+"scale(".concat(i.size/j*(i.flipX?-1:1),", ").concat(i.size/j*(i.flipY?-1:1),") "))+"rotate(".concat(i.rotate,"deg) ")),s["-webkit-transform"]=s.transform),Ft(s)),a=(0<o.length&&(l.style=o),[]);return a.push({tag:"span",attributes:l,children:[n]}),a}var ve=I.styles;function ye(t){var e=t[0],n=t[1],a=m(t.slice(4),1)[0];return{found:!0,width:e,height:n,icon:Array.isArray(a)?{tag:"g",attributes:{class:"".concat(A.cssPrefix,"-").concat(wt.GROUP)},children:[{tag:"path",attributes:{class:"".concat(A.cssPrefix,"-").concat(wt.SECONDARY),fill:"currentColor",d:a[0]}},{tag:"path",attributes:{class:"".concat(A.cssPrefix,"-").concat(wt.PRIMARY),fill:"currentColor",d:a[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:a}}}}var xe={found:!1,width:512,height:512};function we(o,i){var s=i;return"fa"===i&&null!==A.styleDefault&&(i=O),new Promise(function(t,e){var n,a,r;if("fa"===s&&(n=ee(o)||{},o=n.iconName||o,i=n.prefix||i),o&&i&&ve[i]&&ve[i][o])return t(ye(ve[i][o]));a=o,r=i,mt||A.showMissingIcons||!a||console.error('Icon with name "'.concat(a,'" and prefix "').concat(r,'" is missing.')),t(p(p({},xe),{},{icon:A.showMissingIcons&&o&&D("missingIconAbstract")||{}}))})}function ke(){}function Se(t){Ae.mark("".concat(je," ").concat(t," ends")),Ae.measure("".concat(je," ").concat(t),"".concat(je," ").concat(t," begins"),"".concat(je," ").concat(t," ends"))}var Ae=A.measurePerformance&&t&&t.mark&&t.measure?t:{mark:ke,measure:ke},je='FA "7.0.1"',Pe={begin:function(t){return Ae.mark("".concat(je," ").concat(t," begins")),function(){return Se(t)}},end:Se},Fe=function(){};function Ie(t){return"string"==typeof(t.getAttribute?t.getAttribute(w):null)}function Ne(t){return y.createElementNS("http://www.w3.org/2000/svg",t)}function Oe(t){return y.createElement(t)}var Ee={replace:function(t){var e,n=t[0];n.parentNode&&(t[1].forEach(function(t){n.parentNode.insertBefore(function e(n,t){var a,r=(1<arguments.length&&void 0!==t?t:{}).ceFn,o=void 0===r?"svg"===n.tag?Ne:Oe:r;return"string"==typeof n?y.createTextNode(n):(a=o(n.tag),Object.keys(n.attributes||[]).forEach(function(t){a.setAttribute(t,n.attributes[t])}),(n.children||[]).forEach(function(t){a.appendChild(e(t,{ceFn:o}))}),a)}(t),n)}),null===n.getAttribute(w)&&A.keepOriginalSource?(e=y.createComment((e=" ".concat(n.outerHTML," "),"".concat(e,"Font Awesome fontawesome.com "))),n.parentNode.replaceChild(e,n)):n.remove())},nest:function(t){var e=t[0],n=t[1];if(~jt(e).indexOf(A.replacementClass))return Ee.replace(t);var a=new RegExp("".concat(A.cssPrefix,"-.*")),r=(delete n[0].attributes.id,n[0].attributes.class&&(r=n[0].attributes.class.split(" ").reduce(function(t,e){return(e===A.replacementClass||e.match(a)?t.toSvg:t.toNode).push(e),t},{toNode:[],toSvg:[]}),n[0].attributes.class=r.toSvg.join(" "),0===r.toNode.length?e.removeAttribute("class"):e.setAttribute("class",r.toNode.join(" "))),n.map(N).join("\n"));e.setAttribute(w,""),e.innerHTML=r}};function Ce(t){t()}function ze(n,t){var a="function"==typeof t?t:Fe;0===n.length?a():(A.mutateApproach===ct?v.requestAnimationFrame||Ce:Ce)(function(){var t=!0!==A.autoReplaceSvg&&Ee[A.autoReplaceSvg]||Ee.replace,e=Pe.begin("mutate");n.map(t),e(),a()})}var Me=!1;function De(){Me=!0}function Le(){Me=!1}var Te=null;function Re(t){var o,i,s,e;U&&A.observeMutations&&(e=t.treeCallback,o=void 0===e?Fe:e,i=void 0===(e=t.nodeCallback)?Fe:e,s=void 0===(e=t.pseudoElementsCallback)?Fe:e,e=void 0===(e=t.observeMutationsRoot)?y:e,Te=new U(function(t){var r;Me||(r=O,F(t).forEach(function(t){var e,n,a;"childList"===t.type&&0<t.addedNodes.length&&!Ie(t.addedNodes[0])&&(A.searchPseudoElements&&s(t.target),o(t.target)),"attributes"===t.type&&t.target.parentNode&&A.searchPseudoElements&&s([t.target],!0),"attributes"===t.type&&Ie(t.target)&&~xt.indexOf(t.attributeName)&&("class"===t.attributeName&&(e=t.target,n=e.getAttribute?e.getAttribute(st):null,a=e.getAttribute?e.getAttribute(lt):null,n)&&a?(a=(n=ie(jt(t.target))).prefix,n=n.iconName,t.target.setAttribute(st,a||r),n&&t.target.setAttribute(lt,n)):(e=t.target)&&e.classList&&e.classList.contains&&e.classList.contains(A.replacementClass)&&i(t.target))}))}),h)&&Te.observe(e,{childList:!0,attributes:!0,characterData:!0,subtree:!0})}function We(t){var e,n,a=t.getAttribute("data-prefix"),r=t.getAttribute("data-icon"),o=void 0!==t.innerText?t.innerText.trim():"",i=ie(jt(t));return i.prefix||(i.prefix=O),a&&r&&(i.prefix=a,i.iconName=r),i.iconName&&i.prefix||(i.prefix&&0<o.length&&(i.iconName=(e=i.prefix,n=t.innerText,(qt[e]||{})[n]||te(i.prefix,Wt(t.innerText)))),!i.iconName&&A.autoFetchSvg&&t.firstChild&&t.firstChild.nodeType===Node.TEXT_NODE&&(i.iconName=t.firstChild.data)),i}function Ye(t,e){var n,a=1<arguments.length&&void 0!==e?e:{styleParser:!0},r=We(t),o=r.iconName,i=r.prefix,r=r.rest,s=F(t.attributes).reduce(function(t,e){return"class"!==t.name&&"style"!==t.name&&(t[e.name]=e.value),t},{}),l=ce("parseNodeAttributes",{},t),a=a.styleParser?(a=t.getAttribute("style"),n=[],n=a?a.split(";").reduce(function(t,e){var n=e.split(":"),a=n[0],n=n.slice(1);return a&&0<n.length&&(t[a]=n.join(":").trim()),t},{}):n):[];return p({iconName:o,prefix:i,transform:P,mask:{iconName:null,prefix:null,rest:[]},maskId:null,symbol:!1,extra:{classes:r,styles:a,attributes:s}},l)}var Je=I.styles;function He(t){var e="nest"===A.autoReplaceSvg?Ye(t,{styleParser:!1}):Ye(t);return~e.extra.classes.indexOf(vt)?D("generateLayersText",t,e):D("generateSvgReplacementMutation",t,e)}function Be(t){var a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;if(!h)return Promise.resolve();function r(t){e.add("".concat(ft,"-").concat(t))}function o(t){e.remove("".concat(ft,"-").concat(t))}var e=y.documentElement.classList,n=A.autoFetchSvg?[].concat(b($),b(tt)):X.concat(Object.keys(Je)),n=(n.includes("fa")||n.push("fa"),[".".concat(vt,":not([").concat(w,"])")].concat(n.map(function(t){return".".concat(t,":not([").concat(w,"])")})).join(", "));if(0===n.length)return Promise.resolve();var i=[];try{i=F(t.querySelectorAll(n))}catch(t){}if(!(0<i.length))return Promise.resolve();r("pending"),o("complete");var s=Pe.begin("onTree"),l=i.reduce(function(t,e){try{var n=He(e);n&&t.push(n)}catch(t){mt||"MissingIcon"===t.name&&console.error(t)}return t},[]);return new Promise(function(e,n){Promise.all(l).then(function(t){ze(t,function(){r("active"),r("complete"),o("pending"),"function"==typeof a&&a(),s(),e()})}).catch(function(t){s(),n(t)})})}function Ue(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:null;He(t).then(function(t){t&&ze([t],e)})}function Ve(t){var e,n,a,r=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},o=r.transform,i=void 0===o?P:o,s=void 0!==(o=r.symbol)&&o,l=void 0===(o=r.mask)?null:o,f=void 0===(o=r.maskId)?null:o,c=void 0===(o=r.classes)?[]:o,u=void 0===(o=r.attributes)?{}:o,d=void 0===(o=r.styles)?{}:o;if(t)return e=t.prefix,n=t.iconName,a=t.icon,ge(p({type:"icon"},t),function(){return M("beforeDOMElementCreation",{iconDefinition:t,params:r}),pe({icons:{main:ye(a),mask:l?ye(l.icon):{found:!1,width:null,height:null,icon:{}}},prefix:e,iconName:n,transform:p(p({},P),i),symbol:s,maskId:f,extra:{attributes:u,styles:d,classes:c}})})}var i={mixout:function(){return{icon:(r=Ve,function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=(t||{}).icon?t:ue(t||{}),a=(a=e.mask)&&((a||{}).icon?a:ue(a||{}));return r(n,p(p({},e),{},{mask:a}))})};var r},hooks:function(){return{mutationObserverCallbacks:function(t){return t.treeCallback=Be,t.nodeCallback=Ue,t}}},provides:function(t){t.i2svg=function(t){var e=t.node,n=t.callback;return Be(void 0===e?y:e,void 0===n?function(){}:n)},t.generateSvgReplacementMutation=function(r,t){var o=t.iconName,i=t.prefix,s=t.transform,l=t.symbol,e=t.mask,f=t.maskId,c=t.extra;return new Promise(function(a,t){Promise.all([we(o,i),e.iconName?we(e.iconName,e.prefix):Promise.resolve({found:!1,width:512,height:512,icon:{}})]).then(function(t){var e=m(t,2),n=e[0];a([r,pe({icons:{main:n,mask:e[1]},prefix:i,iconName:o,transform:s,symbol:l,maskId:f,extra:c,watchable:!0})])}).catch(t)})},t.generateAbstractIcon=function(t){var e,n=t.children,a=t.attributes,r=t.main,o=t.transform,i=Ft(t.styles);return 0<i.length&&(a.style=i),It(o)&&(e=D("generateAbstractTransformGrouping",{main:r,transform:o,containerWidth:r.width,iconWidth:r.width})),n.push(e||r.icon),{children:n,attributes:a}}}},o={mixout:function(){return{layer:function(t){var n=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},e=n.classes,a=void 0===e?[]:e;return ge({type:"layer"},function(){M("beforeDOMElementCreation",{assembler:t,params:n});var e=[];return t(function(t){Array.isArray(t)?t.map(function(t){e=e.concat(t.abstract)}):e=e.concat(t.abstract)}),[{tag:"span",attributes:{class:["".concat(A.cssPrefix,"-layers")].concat(b(a)).join(" ")},children:e}]})}}}},Q={mixout:function(){return{counter:function(r){var o=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},t=o.title,i=void 0===t?null:t,t=o.classes,s=void 0===t?[]:t,t=o.attributes,l=void 0===t?{}:t,t=o.styles,f=void 0===t?{}:t;return ge({type:"counter",content:r},function(){return M("beforeDOMElementCreation",{content:r,params:o}),t={content:r.toString(),title:i,extra:{attributes:l,styles:f,classes:["".concat(A.cssPrefix,"-layers-counter")].concat(b(s))}},e=t.content,a=p(p({},(n=t.extra).attributes),{},{class:n.classes.join(" ")}),0<(n=Ft(n.styles)).length&&(a.style=n),(n=[]).push({tag:"span",attributes:a,children:[e]}),n;var t,e,n,a})}}}},s={mixout:function(){return{text:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=e.transform,a=void 0===n?P:n,n=e.classes,r=void 0===n?[]:n,n=e.attributes,o=void 0===n?{}:n,n=e.styles,i=void 0===n?{}:n;return ge({type:"text",content:t},function(){return M("beforeDOMElementCreation",{content:t,params:e}),be({content:t,transform:p(p({},P),a),extra:{attributes:o,styles:i,classes:["".concat(A.cssPrefix,"-layers-text")].concat(b(r))}})})}}},provides:function(t){t.generateLayersText=function(t,e){var n,a,r=e.transform,o=e.extra,i=null,s=null;return _&&(n=parseInt(getComputedStyle(t).fontSize,10),i=(a=t.getBoundingClientRect()).width/n,s=a.height/n),Promise.resolve([t,be({content:t.innerHTML,width:i,height:s,transform:r,extra:o,watchable:!0})])}}},_e=new RegExp('"',"ug"),Xe=[1105920,1112319],qe=p(p(p(p({},{FontAwesome:{normal:"fas",400:"fas"}}),{"Font Awesome 7 Free":{900:"fas",400:"far"},"Font Awesome 7 Pro":{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},"Font Awesome 7 Brands":{400:"fab",normal:"fab"},"Font Awesome 7 Duotone":{900:"fad",400:"fadr",normal:"fadr",300:"fadl",100:"fadt"},"Font Awesome 7 Sharp":{900:"fass",400:"fasr",normal:"fasr",300:"fasl",100:"fast"},"Font Awesome 7 Sharp Duotone":{900:"fasds",400:"fasdr",normal:"fasdr",300:"fasdl",100:"fasdt"},"Font Awesome 7 Jelly":{400:"fajr",normal:"fajr"},"Font Awesome 7 Jelly Fill":{400:"fajfr",normal:"fajfr"},"Font Awesome 7 Jelly Duo":{400:"fajdr",normal:"fajdr"},"Font Awesome 7 Slab":{400:"faslr",normal:"faslr"},"Font Awesome 7 Slab Press":{400:"faslpr",normal:"faslpr"},"Font Awesome 7 Thumbprint":{300:"fatl",normal:"fatl"},"Font Awesome 7 Notdog":{900:"fans",normal:"fans"},"Font Awesome 7 Notdog Duo":{900:"fands",normal:"fands"},"Font Awesome 7 Etch":{900:"faes",normal:"faes"},"Font Awesome 7 Chisel":{400:"facr",normal:"facr"},"Font Awesome 7 Whiteboard":{600:"fawsb",normal:"fawsb"}}),{"Font Awesome 5 Free":{900:"fas",400:"far"},"Font Awesome 5 Pro":{900:"fas",400:"far",normal:"far",300:"fal"},"Font Awesome 5 Brands":{400:"fab",normal:"fab"},"Font Awesome 5 Duotone":{900:"fad"}}),{"Font Awesome Kit":{400:"fak",normal:"fak"},"Font Awesome Kit Duotone":{400:"fakd",normal:"fakd"}}),Ke=Object.keys(qe).reduce(function(t,e){return t[e.toLowerCase()]=qe[e],t},{}),Ge=Object.keys(Ke).reduce(function(t,e){var n=Ke[e];return t[e]=n[900]||b(Object.entries(n))[0][1],t},{});function $e(m,h){var g="".concat(it).concat(h.replace(":","-"));return new Promise(function(a,t){var e,r,o,i,s,n,l,f,c,u,d;return null!==m.getAttribute(g)?a():(e=F(m.children).filter(function(t){return t.getAttribute(ot)===h})[0],f=v.getComputedStyle(m,h),l=(d=f.getPropertyValue("font-family")).match(yt),u=f.getPropertyValue("font-weight"),n=f.getPropertyValue("content"),e&&!l?(m.removeChild(e),a()):void(!l||"none"===n||""===n||(n=f.getPropertyValue("content"),c=u,u=d.replace(/^['"]|['"]$/g,"").toLowerCase(),d=parseInt(c),d=isNaN(d)?"normal":d,r=(Ke[u]||{})[d]||Ge[u],d=Wt(b(n.replace(_e,""))[0]||""),u=l[0].startsWith("FontAwesome"),n=(c=f).getPropertyValue("font-feature-settings").includes("ss01"),l=c.getPropertyValue("content").replace(_e,""),f=(f=l.codePointAt(0))>=Xe[0]&&f<=Xe[1],l=2===l.length&&l[0]===l[1],f=f||l||n,l=te(r,d),o=l,u&&(n=Gt[d],u=te("fas",d),(d=n||(u?{prefix:"fas",iconName:u}:null)||{prefix:null,iconName:null}).iconName)&&d.prefix&&(l=d.iconName,r=d.prefix),!l)||f||e&&e.getAttribute(st)===r&&e.getAttribute(lt)===o?a():(m.setAttribute(g,o),e&&m.removeChild(e),(s=(i={iconName:null,prefix:null,transform:P,symbol:!1,mask:{iconName:null,prefix:null,rest:[]},maskId:null,extra:{classes:[],styles:{},attributes:{}}}).extra).attributes[ot]=h,we(l,r).then(function(t){var e=pe(p(p({},i),{},{icons:{main:t,mask:ne()},prefix:r,iconName:o,extra:s,watchable:!0})),n=y.createElementNS("http://www.w3.org/2000/svg","svg");"::before"===h?m.insertBefore(n,m.firstChild):m.appendChild(n),n.outerHTML=e.map(N).join("\n"),m.removeAttribute(g),a()}).catch(t))))})}function Qe(t){return Promise.all([$e(t,"::before"),$e(t,"::after")])}function Ze(t){return!(t.parentNode===document.head||~ut.indexOf(t.tagName.toUpperCase())||t.getAttribute(ot)||t.parentNode&&"svg"===t.parentNode.tagName)}var tn=function(e){return!!e&&dt.some(function(t){return e.includes(t)})};function en(t){var r,e=1<arguments.length&&void 0!==arguments[1]&&arguments[1];if(h){if(e)r=t;else if(A.searchPseudoElementsFullScan)r=t.querySelectorAll("*");else{var n,a=new Set,o=R(document.styleSheets);try{for(o.s();!(n=o.n()).done;){var i=n.value;try{var s,l=R(i.cssRules);try{for(l.s();!(s=l.n()).done;){var f,c=(t=>{if(!t)return[];var e,n=new Set,a=R(t.split(/,(?![^()]*\))/).map(function(t){return t.trim()}).flatMap(function(t){return t.includes("(")?t:t.split(",").map(function(t){return t.trim()})}));try{for(a.s();!(e=a.n()).done;){var r,o=e.value;tn(o)&&""!==(r=dt.reduce(function(t,e){return t.replace(e,"")},o))&&"*"!==r&&n.add(r)}}catch(t){a.e(t)}finally{a.f()}return n})(s.value.selectorText),u=R(c);try{for(u.s();!(f=u.n()).done;){var d=f.value;a.add(d)}}catch(t){u.e(t)}finally{u.f()}}}catch(t){l.e(t)}finally{l.f()}}catch(t){A.searchPseudoElementsWarnings&&console.warn("Font Awesome: cannot parse stylesheet: ".concat(i.href," (").concat(t.message,')\nIf it declares any Font Awesome CSS pseudo-elements, they will not be rendered as SVG icons. Add crossorigin="anonymous" to the <link>, enable searchPseudoElementsFullScan for slower but more thorough DOM parsing, or suppress this warning by setting searchPseudoElementsWarnings to false.'))}}}catch(t){o.e(t)}finally{o.f()}if(!a.size)return;e=Array.from(a).join(", ");try{r=t.querySelectorAll(e)}catch(t){}}return new Promise(function(t,e){var n=F(r).filter(Ze).map(Qe),a=Pe.begin("searchPseudoElements");De(),Promise.all(n).then(function(){a(),Le(),t()}).catch(function(){a(),Le(),e()})})}}function nn(t){return t.toLowerCase().split(" ").reduce(function(t,e){var n=e.toLowerCase().split("-"),a=n[0],r=n.slice(1).join("-");if(a&&"h"===r)t.flipX=!0;else if(a&&"v"===r)t.flipY=!0;else if(r=parseFloat(r),!isNaN(r))switch(a){case"grow":t.size=t.size+r;break;case"shrink":t.size=t.size-r;break;case"left":t.x=t.x-r;break;case"right":t.x=t.x+r;break;case"up":t.y=t.y-r;break;case"down":t.y=t.y+r;break;case"rotate":t.rotate=t.rotate+r}return t},{size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0})}var an,rn=!1,on={x:0,y:0,width:"100%",height:"100%"};function sn(t){return t.attributes&&(t.attributes.fill||(!(1<arguments.length&&void 0!==arguments[1])||arguments[1]))&&(t.attributes.fill="black"),t}an={mixoutsTo:me}.mixoutsTo,r=[et,i,o,Q,s,{hooks:function(){return{mutationObserverCallbacks:function(t){return t.pseudoElementsCallback=en,t}}},provides:function(t){t.pseudoElements2svg=function(t){var e=t.node;A.searchPseudoElements&&en(void 0===e?y:e)}}},{mixout:function(){return{dom:{unwatch:function(){De(),rn=!0}}}},hooks:function(){return{bootstrap:function(){Re(ce("mutationObserverCallbacks",{}))},noAuto:function(){Te&&Te.disconnect()},watch:function(t){var e=t.observeMutationsRoot;rn?Le():Re(ce("mutationObserverCallbacks",{observeMutationsRoot:e}))}}}},{mixout:function(){return{parse:{transform:nn}}},hooks:function(){return{parseNodeAttributes:function(t,e){var n=e.getAttribute("data-fa-transform");return n&&(t.transform=nn(n)),t}}},provides:function(t){t.generateAbstractTransformGrouping=function(t){var e=t.main,n=t.transform,a=t.iconWidth,r={transform:"translate(".concat(t.containerWidth/2," 256)")},o="translate(".concat(32*n.x,", ").concat(32*n.y,") "),i="scale(".concat(n.size/16*(n.flipX?-1:1),", ").concat(n.size/16*(n.flipY?-1:1),") "),n="rotate(".concat(n.rotate," 0 0)"),r={outer:r,inner:{transform:"".concat(o," ").concat(i," ").concat(n)},path:{transform:"translate(".concat(a/2*-1," -256)")}};return{tag:"g",attributes:p({},r.outer),children:[{tag:"g",attributes:p({},r.inner),children:[{tag:e.icon.tag,children:e.icon.children,attributes:p(p({},e.icon.attributes),r.path)}]}]}}}},{hooks:function(){return{parseNodeAttributes:function(t,e){var n=e.getAttribute("data-fa-mask"),n=n?ie(n.split(" ").map(function(t){return t.trim()})):ne();return n.prefix||(n.prefix=O),t.mask=n,t.maskId=e.getAttribute("data-fa-mask-id"),t}}},provides:function(t){t.generateAbstractMask=function(t){var e=t.children,n=t.attributes,a=t.main,r=t.mask,o=t.maskId,i=a.width,a=a.icon,s=r.width,r=r.icon,l=(s=(t={transform:t.transform,containerWidth:s,iconWidth:i}).transform,i=t.iconWidth,l={transform:"translate(".concat(t.containerWidth/2," 256)")},f="translate(".concat(32*s.x,", ").concat(32*s.y,") "),c="scale(".concat(s.size/16*(s.flipX?-1:1),", ").concat(s.size/16*(s.flipY?-1:1),") "),s="rotate(".concat(s.rotate," 0 0)"),{outer:l,inner:{transform:"".concat(f," ").concat(c," ").concat(s)},path:{transform:"translate(".concat(i/2*-1," -256)")}}),f={tag:"rect",attributes:p(p({},on),{},{fill:"white"})},c=a.children?{children:a.children.map(sn)}:{},s={tag:"g",attributes:p({},l.inner),children:[sn(p({tag:a.tag,attributes:p(p({},a.attributes),l.path)},c))]},i={tag:"g",attributes:p({},l.outer),children:[s]},a="mask-".concat(o||At()),c="clip-".concat(o||At()),l={tag:"mask",attributes:p(p({},on),{},{id:a,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[f,i]},s={tag:"defs",children:[{tag:"clipPath",attributes:{id:c},children:"g"===(t=r).tag?t.children:[t]},l]};return e.push(s,{tag:"rect",attributes:p({fill:"currentColor","clip-path":"url(#".concat(c,")"),mask:"url(#".concat(a,")")},on)}),{children:e,attributes:n}}}},{provides:function(t){var o=!1;v.matchMedia&&(o=v.matchMedia("(prefers-reduced-motion: reduce)").matches),t.missingIconAbstract=function(){var t=[],e={fill:"currentColor"},n={attributeType:"XML",repeatCount:"indefinite",dur:"2s"},a=(t.push({tag:"path",attributes:p(p({},e),{},{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"})}),p(p({},n),{},{attributeName:"opacity"})),r={tag:"circle",attributes:p(p({},e),{},{cx:"256",cy:"364",r:"28"}),children:[]};return o||r.children.push({tag:"animate",attributes:p(p({},n),{},{attributeName:"r",values:"28;14;28;28;14;28;"})},{tag:"animate",attributes:p(p({},a),{},{values:"1;0;1;1;0;1;"})}),t.push(r),t.push({tag:"path",attributes:p(p({},e),{},{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),children:o?[]:[{tag:"animate",attributes:p(p({},a),{},{values:"1;0;0;0;0;1;"})}]}),o||t.push({tag:"path",attributes:p(p({},e),{},{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),children:[{tag:"animate",attributes:p(p({},a),{},{values:"0;0;1;1;0;0;"})}]}),{tag:"g",attributes:{class:"missing"},children:t}}}},{hooks:function(){return{parseNodeAttributes:function(t,e){var n=e.getAttribute("data-fa-symbol");return t.symbol=null!==n&&(""===n||n),t}}}}],C={},Object.keys(z).forEach(function(t){-1===fe.indexOf(t)&&delete z[t]}),r.forEach(function(t){var e,n=t.mixout?t.mixout():{};Object.keys(n).forEach(function(e){"function"==typeof n[e]&&(an[e]=n[e]),"object"===J(n[e])&&Object.keys(n[e]).forEach(function(t){an[e]||(an[e]={}),an[e][t]=n[e][t]})}),t.hooks&&(e=t.hooks(),Object.keys(e).forEach(function(t){C[t]||(C[t]=[]),C[t].push(e[t])})),t.provides&&t.provides(z)}),function(t){try{for(var e=arguments.length,n=new Array(1<e?e-1:0),a=1;a<e;a++)n[a-1]=arguments[a];t.apply(void 0,n)}catch(t){if(!mt)throw t}}(function(){V&&(v.FontAwesome||(v.FontAwesome=me),Dt(function(){he(),M("bootstrap")})),I.hooks=p(p({},I.hooks),{},{addPack:function(t,e){I.styles[t]=p(p({},I.styles[t]||{}),e),Zt(),he()},addPacks:function(t){t.forEach(function(t){var e=m(t,2),n=e[0],e=e[1];I.styles[n]=p(p({},I.styles[n]||{}),e)}),Zt(),he()},addShims:function(t){var e;(e=I.shims).push.apply(e,b(t)),Zt(),he()}})})})();