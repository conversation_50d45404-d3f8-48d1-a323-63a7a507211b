import axios from "axios";

const API_BASE_URL = "http://localhost/eduexamin/api";

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Secure token storage utility
const TokenStorage = {
  get: () => {
    try {
      // For now, we'll use localStorage but with additional security measures
      // In production, consider using httpOnly cookies or secure session storage
      const token = localStorage.getItem("token");
      if (token) {
        // Basic validation - check if token looks valid
        const parts = token.split('.');
        if (parts.length === 3) {
          return token;
        }
      }
      return null;
    } catch (error) {
      console.error("Error retrieving token:", error);
      return null;
    }
  },

  set: (token) => {
    try {
      if (token && typeof token === 'string') {
        localStorage.setItem("token", token);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Error storing token:", error);
      return false;
    }
  },

  remove: () => {
    try {
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      return true;
    } catch (error) {
      console.error("Error removing token:", error);
      return false;
    }
  }
};

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = TokenStorage.get();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token expiration and errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      TokenStorage.remove();
      // Redirect to appropriate login page based on current path
      const currentPath = window.location.pathname;
      if (currentPath.includes('/admin')) {
        window.location.href = "/admin/login";
      } else {
        window.location.href = "/login";
      }
    }
    return Promise.reject(error);
  }
);

// Export both the api instance and TokenStorage for use in auth components
export { TokenStorage };
export default api;
