import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import api from "../services/api";
import "../styles/attemptDetails.css";
import { HashLoader } from "react-spinners";
import { Book, BarChart2, Check, Clock, Send, Info } from "lucide-react";
const AttemptDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [attempt, setAttempt] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchAttemptDetails();
  }, [id]);

  const fetchAttemptDetails = async () => {
    try {
      const response = await api.get(`/attempts/${id}`);
      setAttempt(response.data.data || response.data);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching attempt details:", error);
      setError("Failed to load attempt details");
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
    );
  }

  if (error || !attempt) {
    return (
      <div className="attempt-error">
        <div className="error-message">
          <p className="error-title">Error</p>
          <p>{error || "Attempt not found"}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="attempt-container">
      <div className="attempt-card">
        <div className="attempt-header">
          <h1 className="attempt-title">
            <Info size={28} color="#1c2a45" />
            {attempt.type === "exam" ? "Exam" : "Quiz"} Details
          </h1>

          <div className="attempt-info-grid">
            <div className="info-box">
              <Book size={24} color="#4A90E2" />
              <h3>Subject</h3>
              <p>{attempt.subject?.name || attempt.exam?.subject?.name}</p>
            </div>
            <div className="info-box">
              <BarChart2 size={24} color="#27AE60" />
              <h3>Score</h3>
              <p>{attempt.score}% from 100%</p>
            </div>
            <div className="info-box">
              <Check size={24} color="#ff0050" />
              <h3>Status</h3>
              <p>{attempt.submitted_at ? "Completed" : "In Progress"}</p>
            </div>
            <div className="info-box">
              <Clock size={24} color="#F39C12" />
              <h3>Started At</h3>
              <p>{new Date(attempt.started_at).toLocaleString()}</p>
            </div>
            {attempt.submitted_at && (
              <div className="info-box">
                <Send size={26} color="#9B59B6" />
                <h3>Submitted At</h3>
                <p>{new Date(attempt.submitted_at).toLocaleString()}</p>
              </div>
            )}
          </div>
        </div>

        <div className="attempt-questions">
          <h2>Questions & Answers</h2>
          {attempt.attempt_questions.map((question, index) => (
            <div key={question.id} className="question-box">
              <div className="question-header">
                <h3>Question {index + 1}</h3>
                <p>{question.question_text}</p>
                {question.direction && (
                  <p
                    className={`question-direction ${
                      question?.direction === "ltr" ? "ltr" : "rtl"
                    }`}
                  >
                    {question.direction}
                  </p>
                )}
              </div>

              <div className="options-list">
                {question.attempt_options.map((option) => (
                  <div
                    key={option.id}
                    className={`option-box ${
                      option.is_correct
                        ? "correct"
                        : option.selected
                        ? "incorrect"
                        : "netural"
                    }`}
                  >
                    {/* <div className="option-indicator">
                      {(option.is_correct || option.selected) && (
                        <div className="indicator-dot" />
                      )}
                    </div> */}
                    <span className="option-text">{option.option_text}</span>
                    {option.direction && (
                      <span className="option-direction">
                        ({option.direction})
                      </span>
                    )}
                  </div>
                ))}
              </div>

              <div className="answer-summary">
                <p>
                  <strong>Your Answer:</strong>{" "}
                  {question.attempt_options
                    .filter((opt) => opt.selected)
                    .map((opt) => opt.option_text)
                    .join(", ") || "No answer selected"}
                </p>
                <p>
                  <strong>Correct Answer:</strong>{" "}
                  {question.attempt_options
                    .filter((opt) => opt.is_correct)
                    .map((opt) => opt.option_text)
                    .join(", ")}
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="back-button-container">
          <button
            onClick={() => navigate("/student/results")}
            className="back-button"
          >
            Back to Results
          </button>
        </div>
      </div>
    </div>
  );
};

export default AttemptDetails;
