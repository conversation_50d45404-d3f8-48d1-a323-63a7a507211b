import { useState, useEffect } from "react";
import { NavLink } from "react-router-dom";
import api from "../../services/api";
import "../../styles/studentContent.css";
import Landing from "../../assets/landing2.png";
import {
  BookOpen,
  GraduationCap,
  BarChart3,
  Play,
  Trophy,
  Clock,
  Target,
  TrendingUp,
  Calendar,
  Users
} from "lucide-react";
import { HashLoader } from "react-spinners";

const StudentHome = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const response = await api.get("/dashboard");
      setDashboardData(response.data.data || response.data);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      setError("Failed to load dashboard data");
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <HashLoader
        color="#1c2a45"
        size={50}
        cssOverride={{
          display: "block",
          margin: "0px auto",
          position: "absolute",
          left: "50%",
          top: "50%",
          transform: "translate(-50%, -50%)",
        }}
      />
    );
  }

  if (error) {
    return (
      <div className="student-hero">
        <div className="title">
          <BarChart3 size={30} />
          <h1>Student Dashboard</h1>
        </div>
        <div className="image">
          <img src={Landing} alt="student-dashboard-landing" />
        </div>
        <p>Welcome to your student dashboard. Here you can take exams and view your results.</p>
        <div className="form-error" style={{ textAlign: "center", margin: "1rem 0" }}>
          {error}
        </div>
        <div className={"nav-container"}>
          <NavLink className="link" to={"/student/start-quiz"}>
            <Play size={22} />
          </NavLink>
          <NavLink className="link" to={"/student/exams"}>
            <GraduationCap size={22} />
          </NavLink>
          <NavLink className="link" to={"/student/results"}>
            <BookOpen size={22} />
          </NavLink>
        </div>
      </div>
    );
  }

  const stats = dashboardData?.statistics || {};
  const recentAttempts = dashboardData?.recent_attempts || [];
  const availableExams = dashboardData?.available_exams || [];
  const upcomingExams = dashboardData?.upcoming_exams || [];
  const subjects = dashboardData?.subjects || [];

  return (
    <div>
      <div className="student-hero">
        <div className="title">
          <BarChart3 size={30} />
          <h1>Welcome back, {dashboardData?.student_info?.name || 'Student'}!</h1>
        </div>
        <div className="image">
          <img src={Landing} alt="student-dashboard-landing" />
        </div>
        <p>
          Here's your learning progress and available activities.
        </p>

        {/* Quick Stats */}
        <div className="dashboard-stats">
          <div className="stat-card">
            <Trophy size={24} color="#FFD700" />
            <div className="stat-info">
              <h3>{stats.total_attempts || 0}</h3>
              <p>Total Attempts</p>
            </div>
          </div>
          <div className="stat-card">
            <Target size={24} color="#4CAF50" />
            <div className="stat-info">
              <h3>{stats.completed_attempts || 0}</h3>
              <p>Completed</p>
            </div>
          </div>
          <div className="stat-card">
            <TrendingUp size={24} color="#2196F3" />
            <div className="stat-info">
              <h3>{stats.average_score ? `${stats.average_score}%` : 'N/A'}</h3>
              <p>Avg Score</p>
            </div>
          </div>
          <div className="stat-card">
            <Users size={24} color="#9C27B0" />
            <div className="stat-info">
              <h3>{subjects.length}</h3>
              <p>Subjects</p>
            </div>
          </div>
        </div>

        <div className={"nav-container"}>
          <NavLink className="link" to={"/student/start-quiz"} title="Start Quiz">
            <Play size={22} />
          </NavLink>
          <NavLink className="link" to={"/student/exams"} title="Available Exams">
            <GraduationCap size={22} />
          </NavLink>
          <NavLink className="link" to={"/student/results"} title="My Results">
            <BookOpen size={22} />
          </NavLink>
        </div>
      </div>

      {/* Recent Activity & Quick Access */}
      <div className="dashboard-content">
        {/* Available Exams */}
        {availableExams.length > 0 && (
          <div className="dashboard-section">
            <h2><GraduationCap size={20} /> Available Exams</h2>
            <div className="exam-list">
              {availableExams.slice(0, 3).map((exam) => (
                <div key={exam.id} className="exam-item">
                  <h4>{exam.title}</h4>
                  <p>{exam.subject?.name}</p>
                  <span className="exam-duration">{exam.duration} min</span>
                </div>
              ))}
            </div>
            {availableExams.length > 3 && (
              <NavLink to="/student/exams" className="view-all-link">
                View all {availableExams.length} exams →
              </NavLink>
            )}
          </div>
        )}

        {/* Upcoming Exams */}
        {upcomingExams.length > 0 && (
          <div className="dashboard-section">
            <h2><Calendar size={20} /> Upcoming Exams</h2>
            <div className="exam-list">
              {upcomingExams.slice(0, 3).map((exam) => (
                <div key={exam.id} className="exam-item upcoming">
                  <h4>{exam.title}</h4>
                  <p>{exam.subject?.name}</p>
                  <span className="exam-start">
                    <Clock size={16} />
                    {new Date(exam.start_time).toLocaleDateString()}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Recent Attempts */}
        {recentAttempts.length > 0 && (
          <div className="dashboard-section">
            <h2><BarChart3 size={20} /> Recent Activity</h2>
            <div className="attempts-list">
              {recentAttempts.map((attempt) => (
                <div key={attempt.id} className="attempt-item">
                  <div className="attempt-info">
                    <h4>{attempt.type === 'exam' ? 'Exam' : 'Quiz'}</h4>
                    <p>{attempt.subject?.name || attempt.exam?.subject?.name}</p>
                    <span className="attempt-date">
                      {new Date(attempt.started_at).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="attempt-score">
                    {attempt.submitted_at ? (
                      <span className="score">{attempt.score}%</span>
                    ) : (
                      <span className="in-progress">In Progress</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
            <NavLink to="/student/results" className="view-all-link">
              View all results →
            </NavLink>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentHome;
