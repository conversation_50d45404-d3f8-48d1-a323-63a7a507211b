import "../../styles/studentContent.css";
import Landing from "../../assets/landing2.png";
import { NavLink } from "react-router-dom";
import { BookOpen, GraduationCap, BarChart3, Play } from "lucide-react";

const StudentHome = () => {
  return (
    <div>
      <div className="student-hero">
        <div className="title">
          <BarChart3 size={30} />
          <h1>Student Dashboard</h1>
        </div>
        <div className="image">
          <img src={Landing} alt="student-dashboard-landing" />
        </div>
        <p>
          Welcome to your student dashboard. Here you can take exams and view
          your results.
        </p>
        <div className={"nav-container"}>
          <NavLink className="link" to={"/student/start-quiz"}>
            <Play size={22} />
          </NavLink>
          <NavLink className="link" to={"/student/exams"}>
            <GraduationCap size={22} />
          </NavLink>
          <NavLink className="link" to={"/student/results"}>
            <BookOpen size={22} />
          </NavLink>
        </div>
      </div>
    </div>
  );
};

export default StudentHome;
