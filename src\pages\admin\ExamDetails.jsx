import { useEffect, useMemo, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import api from '../../services/api';
import { ArrowLeft, Search } from 'lucide-react';

// Helper: format datetime-local value to SQL-like local datetime
const toLocalSql = (value) => {
  if (!value) return null;
  const [date, time] = value.split('T');
  const [hh = '00', mm = '00', ss = '00'] = (time || '').split(':');
  return `${date} ${hh}:${mm}:${ss || '00'}`;
};

const ExamDetails = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [exam, setExam] = useState(null);
  const [subjects, setSubjects] = useState([]);
  const [questions, setQuestions] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [registrations, setRegistrations] = useState([]);

  // Editable fields
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [subjectId, setSubjectId] = useState('');
  const [startTime, setStartTime] = useState(''); // datetime-local string
  const [endTime, setEndTime] = useState('');
  const [duration, setDuration] = useState(60);

  // Question filters
  const [qSearch, setQSearch] = useState('');
  const [qSubject, setQSubject] = useState('');
  const [qType, setQType] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [randomCount, setRandomCount] = useState('');
  const [questionIds, setQuestionIds] = useState([]);

  const isStep1Valid = useMemo(() => {
    return Boolean(subjectId) && Boolean(title.trim());
  }, [subjectId, title]);

  const isStep2Valid = useMemo(() => {
    if (!startTime || !endTime || !duration) return false;
    return new Date(endTime) > new Date(startTime);
  }, [startTime, endTime, duration]);

  const canEditCore = useMemo(() => {
    if (!exam) return false;
    try {
      const now = new Date();
      return new Date(exam.start_time) > now; // allow editing until not started
    } catch {
      return false;
    }
  }, [exam]);

  const load = async () => {
    setIsLoading(true);
    setError('');
    try {
      const [examRes, subjectsRes, questionsRes] = await Promise.all([
        api.get(`/admin/exams/${id}`),
        api.get('/admin/subjects'),
        api.get('/admin/questions', { params: { page: 1, per_page: 200 } }),
      ]);
      const e = examRes.data;
      setExam(e);
      setTitle(e.title || '');
      setRegistrations(e.registrations || []);
      setDescription(e.description || '');
      setSubjectId(e.subject_id || '');
      setStartTime(e.start_time ? e.start_time.replace(' ', 'T').slice(0, 16) : '');
      setEndTime(e.end_time ? e.end_time.replace(' ', 'T').slice(0, 16) : '');
      setDuration(e.duration || 60);
      setSubjects(subjectsRes.data.data || subjectsRes.data || []);
      setQuestions(questionsRes.data.data || []);
      setQuestionIds((e.questions || []).map((q) => q.id));
    } catch (e) {
      console.error(e);
      setError('Failed to load exam');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    load();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const filteredQuestions = useMemo(() => {
    const search = qSearch.trim().toLowerCase();
    return (questions || []).filter((q) => {
      if (qSubject && String(q.subject_id) !== String(qSubject)) return false;
      if (qType && q.type !== qType) return false;
      if (!search) return true;
      return (q.question_text || '').toLowerCase().includes(search);
    });
  }, [questions, qSearch, qSubject, qType]);

  const saveMeta = async () => {
    if (!exam) return;
    setSaving(true);
    setError('');
    try {
      await api.put(`/admin/exams/${exam.id}`, {
        subject_id: Number(subjectId) || undefined,
        title: title.trim(),
        description: description || null,
        start_time: startTime ? toLocalSql(startTime) : undefined,
        end_time: endTime ? toLocalSql(endTime) : undefined,
        duration: Number(duration) || undefined,
      });
      await load();
    } catch (err) {
      console.error(err);
      const apiErrors = err.response?.data?.errors;
      if (apiErrors && typeof apiErrors === 'object') {
        const messages = Object.entries(apiErrors).map(([field, msgs]) => `${field}: ${Array.isArray(msgs) ? msgs[0] : String(msgs)}`);
        setError(messages.join(' | '));
      } else {
        setError(err.response?.data?.message || 'Failed to save');
      }
    } finally {
      setSaving(false);
    }
  };

  const attachQuestion = async (questionId) => {
    if (!exam) return;
    try {
      await api.post(`/admin/exams/${exam.id}/questions`, { question_ids: [questionId] });
      await load();
    } catch (err) {
      console.error(err);
      setError('Failed to attach question');
    }
  };

  const detachQuestion = async (questionId) => {
    if (!exam) return;
    try {
      await api.delete(`/admin/exams/${exam.id}/questions/${questionId}`);
      await load();
    } catch (err) {
      console.error(err);
      setError('Failed to detach question');
    }
  };

  const submitAll = async (e) => {
    e.preventDefault();
    if (!exam) return;
    if (!(isStep1Valid && isStep2Valid)) return;
    setSaving(true);
    setError('');
    try {
      await api.put(`/admin/exams/${exam.id}`, {
        subject_id: Number(subjectId) || undefined,
        title: title.trim(),
        description: description || null,
        start_time: startTime ? toLocalSql(startTime) : undefined,
        end_time: endTime ? toLocalSql(endTime) : undefined,
        duration: Number(duration) || undefined,
      });
      const existingIds = (exam.questions || []).map((q) => q.id);
      const toAdd = questionIds.filter((id) => !existingIds.includes(id));
      const toRemove = existingIds.filter((id) => !questionIds.includes(id));
      if (toAdd.length > 0) {
        await api.post(`/admin/exams/${exam.id}/questions`, { question_ids: toAdd });
      }
      for (const qid of toRemove) {
        await api.delete(`/admin/exams/${exam.id}/questions/${qid}`);
      }
      await load();
    } catch (err) {
      console.error(err);
      const apiErrors = err.response?.data?.errors;
      if (apiErrors && typeof apiErrors === 'object') {
        const messages = Object.entries(apiErrors).map(([field, msgs]) => `${field}: ${Array.isArray(msgs) ? msgs[0] : String(msgs)}`);
        setError(messages.join(' | '));
      } else {
        setError(err.response?.data?.message || 'Failed to save');
      }
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <button onClick={() => navigate(-1)} className="px-3 py-1 border rounded hover:bg-gray-50">
            <ArrowLeft className="w-4 h-4 inline" /> Back
          </button>
          <h1 className="text-2xl font-bold text-gray-900">Exam Details</h1>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center min-h-[200px]">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      ) : !exam ? (
        <div className="text-gray-600">Exam not found.</div>
      ) : (
        <div className="space-y-6">
          {error && (
            <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded">{error}</div>
          )}

          {/* Registrations table */}
          <div className="bg-white shadow rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold">Registrations ({registrations.length})</h2>
              <a href="#meta" className="text-sm text-blue-600 hover:underline">Edit exam</a>
            </div>
            {registrations.length === 0 ? (
              <div className="text-gray-600">No registrations yet.</div>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Student</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                      <th className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered At</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {registrations.map((r, idx) => (
                      <tr key={r.id}>
                        <td className="px-4 py-2 text-sm text-gray-700">{idx + 1}</td>
                        <td className="px-4 py-2 text-sm text-gray-900">{r.student?.name || '—'}</td>
                        <td className="px-4 py-2 text-sm text-gray-700">{r.student?.email || '—'}</td>
                        <td className="px-4 py-2 text-sm text-gray-700">{r.created_at ? new Date(r.created_at).toLocaleString() : '—'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>

          <div className="bg-white shadow rounded-lg p-6">
            <form onSubmit={submitAll} className="space-y-4">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <span className={`px-2 py-1 rounded ${currentStep === 1 ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700'}`}>1. Basic</span>
                  <span className={`px-2 py-1 rounded ${currentStep === 2 ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700'}`}>2. Schedule</span>
                  <span className={`px-2 py-1 rounded ${currentStep === 3 ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700'}`}>3. Questions</span>
                </div>
                <div className="text-gray-500">Step {currentStep} of 3</div>
              </div>

              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                      <select
                        disabled={!canEditCore}
                        value={subjectId}
                        onChange={(e) => setSubjectId(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                      >
                        <option value="">Select subject</option>
                        {subjects.map((s) => (
                          <option key={s.id} value={s.id}>{s.name}</option>
                        ))}
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                      <input
                        type="text"
                        value={title}
                        onChange={(e) => setTitle(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea
                      rows={3}
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    />
                  </div>
                </div>
              )}

              {currentStep === 2 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Start Time</label>
                      <input
                        disabled={!canEditCore}
                        type="datetime-local"
                        value={startTime}
                        onChange={(e) => setStartTime(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">End Time</label>
                      <input
                        disabled={!canEditCore}
                        type="datetime-local"
                        value={endTime}
                        onChange={(e) => setEndTime(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Duration (minutes)</label>
                      <input
                        type="number"
                        value={duration}
                        onChange={(e) => setDuration(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md"
                      />
                    </div>
                  </div>
                </div>
              )}

              {currentStep === 3 && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">Attach Questions</label>
                    <div className="text-xs text-gray-500">Selected: {questionIds.length}</div>
                  </div>

                  <div className="bg-gray-50 p-3 rounded border mb-3">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-3">
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Subject</label>
                        <select
                          disabled={!canEditCore}
                          value={qSubject}
                          onChange={(e) => setQSubject(e.target.value)}
                          className="w-full px-2 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                        >
                          <option value="">All</option>
                          {subjects.map((s) => (
                            <option key={s.id} value={s.id}>{s.name}</option>
                          ))}
                        </select>
                      </div>
                      <div>
                        <label className="block text-xs text-gray-600 mb-1">Type</label>
                        <select
                          disabled={!canEditCore}
                          value={qType}
                          onChange={(e) => setQType(e.target.value)}
                          className="w-full px-2 py-2 border border-gray-300 rounded-md disabled:bg-gray-100"
                        >
                          <option value="">All</option>
                          <option value="mcq">Multiple Choice</option>
                          <option value="true_false">True / False</option>
                        </select>
                      </div>
                      <div className="md:col-span-2">
                        <label className="block text-xs text-gray-600 mb-1">Search</label>
                        <div className="flex items-center">
                          <input
                            disabled={!canEditCore}
                            type="text"
                            value={qSearch}
                            onChange={(e) => setQSearch(e.target.value)}
                            placeholder="Search question text..."
                            className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md disabled:bg-gray-100"
                          />
                          <button
                            disabled={!canEditCore}
                            type="button"
                            onClick={async () => {
                              try {
                                const res = await api.get('/admin/questions', {
                                  params: {
                                    page: 1,
                                    per_page: 200,
                                    search: qSearch || undefined,
                                    subject_id: qSubject || undefined,
                                    type: qType || undefined,
                                  },
                                });
                                setQuestions(res.data.data || []);
                              } catch (err) {
                                console.error(err);
                              }
                            }}
                            className="px-3 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 disabled:opacity-50"
                          >
                            <Search className="w-4 h-4" />
                            Search
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3 mb-3">
                    <label className="text-sm text-gray-700">Select random</label>
                    <input
                      type="number"
                      min={0}
                      max={filteredQuestions.length}
                      value={randomCount}
                      onChange={(e) => setRandomCount(e.target.value)}
                      className="w-28 px-2 py-1 border rounded"
                      placeholder="Count"
                      disabled={!canEditCore}
                    />
                    <button
                      type="button"
                      onClick={() => {
                        const n = Math.max(0, Math.min(Number(randomCount || 0), filteredQuestions.length));
                        const ids = filteredQuestions.map((q) => q.id);
                        for (let i = ids.length - 1; i > 0; i--) {
                          const j = Math.floor(Math.random() * (i + 1));
                          [ids[i], ids[j]] = [ids[j], ids[i]];
                        }
                        const selected = ids.slice(0, n);
                        setQuestionIds(selected);
                      }}
                      className="px-3 py-1 border rounded hover:bg-gray-50"
                      disabled={!canEditCore}
                    >
                      Apply
                    </button>
                  </div>

                  <div className="border rounded max-h-72 overflow-auto p-2 grid grid-cols-1 md:grid-cols-2 gap-2 bg-white">
                    {filteredQuestions.map((q) => {
                      const checked = questionIds.includes(q.id);
                      return (
                        <label key={q.id} className={`block p-3 border rounded cursor-pointer hover:bg-gray-50 ${checked ? 'border-blue-300 bg-blue-50' : 'border-gray-200'}`}>
                          <div className="flex items-start gap-2">
                            <input
                              type="checkbox"
                              disabled={!canEditCore}
                              checked={checked}
                              onChange={(e) => {
                                const isChecked = e.target.checked;
                                setQuestionIds((prev) => (
                                  isChecked ? [...prev, q.id] : prev.filter((id) => id !== q.id)
                                ));
                              }}
                              className="mt-1"
                            />
                            <div>
                              <div className="text-sm font-medium text-gray-900">Q#{q.id} • {q.subject?.name} • {q.type === 'mcq' ? 'MCQ' : 'T/F'}</div>
                              <div className="text-sm text-gray-700" title={q.question_text}>{q.question_text}</div>
                            </div>
                          </div>
                        </label>
                      );
                    })}
                    {filteredQuestions.length === 0 && (
                      <div className="text-center text-gray-500 py-8 col-span-2">No questions found for the selected filters.</div>
                    )}
                  </div>
                </div>
              )}

              <div className="flex justify-between items-center gap-3">
                <div>
                  <button
                    type="button"
                    onClick={() => setCurrentStep((s) => Math.max(1, s - 1))}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                    disabled={currentStep === 1}
                  >
                    Back
                  </button>
                </div>
                <div className="flex items-center gap-3">
                  {currentStep < 3 && (
                    <button
                      type="button"
                      onClick={() => setCurrentStep((s) => Math.min(3, s + 1))}
                      disabled={(currentStep === 1 && !isStep1Valid) || (currentStep === 2 && !isStep2Valid)}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                      Next
                    </button>
                  )}
                  {currentStep === 3 && (
                    <button
                      type="submit"
                      disabled={!isStep1Valid || !isStep2Valid || !canEditCore || saving}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                    >
                      Save Changes
                    </button>
                  )}
                </div>
              </div>
            </form>
          </div>

                  </div>
      )}
    </div>
  );
};

export default ExamDetails;