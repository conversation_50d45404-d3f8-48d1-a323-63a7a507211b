import React, { createContext, useContext, useState, useEffect } from 'react';
import api from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored auth data on mount
    const storedToken = localStorage.getItem('token');
    const storedUser = localStorage.getItem('user');

    if (storedToken && storedUser) {
      const parsedUser = JSON.parse(storedUser);
      setToken(storedToken);
      setUser(parsedUser);
    }
    setIsLoading(false);
  }, []);

  const login = async (email, password, type) => {
    try {
      const endpoint = type === 'admin' ? '/auth/admin/login' : '/auth/student/login';
      const response = await api.post(endpoint, { email, password });

      const { user: userData, token: authToken, type: userType } = response.data;

      // Add type to user object
      const userWithType = { ...userData, type: userType };

      setUser(userWithType);
      setToken(authToken);

      localStorage.setItem('token', authToken);
      localStorage.setItem('user', JSON.stringify(userWithType));

      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: error.response?.data?.message || 'Login failed' };
    }
  };

  const register = async (data, type) => {
    try {
      const endpoint = type === 'admin' ? '/auth/admin/register' : '/auth/student/register';
      const response = await api.post(endpoint, data);

      const { user: userData, token: authToken, type: userType } = response.data;

      // Add type to user object
      const userWithType = { ...userData, type: userType };

      setUser(userWithType);
      setToken(authToken);

      localStorage.setItem('token', authToken);
      localStorage.setItem('user', JSON.stringify(userWithType));

      return { success: true };
    } catch (error) {
      return { success: false, error: error.response?.data?.message || 'Registration failed' };
    }
  };

  const logout = () => {
    setUser(null);
    setToken(null);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  };

  const value = {
    user,
    token,
    login,
    register,
    logout,
    isLoading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};