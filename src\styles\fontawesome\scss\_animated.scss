// animating icons
// --------------------------
@use 'variables' as v;

.#{v.$css-prefix}-beat {
  animation-name: #{v.$css-prefix}-beat;
  animation-delay: var(--#{v.$css-prefix}-animation-delay, 0s);
  animation-direction: var(--#{v.$css-prefix}-animation-direction, normal);
  animation-duration: var(--#{v.$css-prefix}-animation-duration, 1s);
  animation-iteration-count: var(--#{v.$css-prefix}-animation-iteration-count, infinite);
  animation-timing-function: var(--#{v.$css-prefix}-animation-timing, ease-in-out);
}

.#{v.$css-prefix}-bounce {
  animation-name: #{v.$css-prefix}-bounce;
  animation-delay: var(--#{v.$css-prefix}-animation-delay, 0s);
  animation-direction: var(--#{v.$css-prefix}-animation-direction, normal);
  animation-duration: var(--#{v.$css-prefix}-animation-duration, 1s);
  animation-iteration-count: var(--#{v.$css-prefix}-animation-iteration-count, infinite);
  animation-timing-function: var(--#{v.$css-prefix}-animation-timing, cubic-bezier(0.280, 0.840, 0.420, 1));
}

.#{v.$css-prefix}-fade {
  animation-name: #{v.$css-prefix}-fade;
  animation-delay: var(--#{v.$css-prefix}-animation-delay, 0s);
  animation-direction: var(--#{v.$css-prefix}-animation-direction, normal);
  animation-duration: var(--#{v.$css-prefix}-animation-duration, 1s);
  animation-iteration-count: var(--#{v.$css-prefix}-animation-iteration-count, infinite);
  animation-timing-function: var(--#{v.$css-prefix}-animation-timing, cubic-bezier(.4,0,.6,1));
}

.#{v.$css-prefix}-beat-fade {
  animation-name: #{v.$css-prefix}-beat-fade;
  animation-delay: var(--#{v.$css-prefix}-animation-delay, 0s);
  animation-direction: var(--#{v.$css-prefix}-animation-direction, normal);
  animation-duration: var(--#{v.$css-prefix}-animation-duration, 1s);
  animation-iteration-count: var(--#{v.$css-prefix}-animation-iteration-count, infinite);
  animation-timing-function: var(--#{v.$css-prefix}-animation-timing, cubic-bezier(.4,0,.6,1));
}

.#{v.$css-prefix}-flip {
  animation-name: #{v.$css-prefix}-flip;
  animation-delay: var(--#{v.$css-prefix}-animation-delay, 0s);
  animation-direction: var(--#{v.$css-prefix}-animation-direction, normal);
  animation-duration: var(--#{v.$css-prefix}-animation-duration, 1s);
  animation-iteration-count: var(--#{v.$css-prefix}-animation-iteration-count, infinite);
  animation-timing-function: var(--#{v.$css-prefix}-animation-timing, ease-in-out);
}

.#{v.$css-prefix}-shake {
  animation-name: #{v.$css-prefix}-shake;
  animation-delay: var(--#{v.$css-prefix}-animation-delay, 0s);
  animation-direction: var(--#{v.$css-prefix}-animation-direction, normal);
  animation-duration: var(--#{v.$css-prefix}-animation-duration, 1s);
  animation-iteration-count: var(--#{v.$css-prefix}-animation-iteration-count, infinite);
  animation-timing-function: var(--#{v.$css-prefix}-animation-timing, linear);
}

.#{v.$css-prefix}-spin {
  animation-name: #{v.$css-prefix}-spin;
  animation-delay: var(--#{v.$css-prefix}-animation-delay, 0s);
  animation-direction: var(--#{v.$css-prefix}-animation-direction, normal);
  animation-duration: var(--#{v.$css-prefix}-animation-duration, 2s);
  animation-iteration-count: var(--#{v.$css-prefix}-animation-iteration-count, infinite);
  animation-timing-function: var(--#{v.$css-prefix}-animation-timing, linear);
}

.#{v.$css-prefix}-spin-reverse {
  --#{v.$css-prefix}-animation-direction: reverse;
}

.#{v.$css-prefix}-pulse,
.#{v.$css-prefix}-spin-pulse {
  animation-name: #{v.$css-prefix}-spin;
  animation-direction: var(--#{v.$css-prefix}-animation-direction, normal);
  animation-duration: var(--#{v.$css-prefix}-animation-duration, 1s);
  animation-iteration-count: var(--#{v.$css-prefix}-animation-iteration-count, infinite);
  animation-timing-function: var(--#{v.$css-prefix}-animation-timing, steps(8));
}

// if agent or operating system prefers reduced motion, disable animations
// see: https://www.smashingmagazine.com/2020/09/design-reduced-motion-sensitivities/
// see: https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion
@media (prefers-reduced-motion: reduce) {
  .#{v.$css-prefix}-beat,
  .#{v.$css-prefix}-bounce,
  .#{v.$css-prefix}-fade,
  .#{v.$css-prefix}-beat-fade,
  .#{v.$css-prefix}-flip,
  .#{v.$css-prefix}-pulse,
  .#{v.$css-prefix}-shake,
  .#{v.$css-prefix}-spin,
  .#{v.$css-prefix}-spin-pulse {
    animation: none !important;
    transition: none !important;
  }
}

@keyframes #{v.$css-prefix}-beat {
  0%, 90% { transform: scale(1); }
  45% { transform: scale(var(--#{v.$css-prefix}-beat-scale, 1.25)); }
}

@keyframes #{v.$css-prefix}-bounce {
  0%   { transform: scale(1,1) translateY(0); }
  10%  { transform: scale(var(--#{v.$css-prefix}-bounce-start-scale-x, 1.1),var(--#{v.$css-prefix}-bounce-start-scale-y, 0.9)) translateY(0); }
  30%  { transform: scale(var(--#{v.$css-prefix}-bounce-jump-scale-x, 0.9),var(--#{v.$css-prefix}-bounce-jump-scale-y, 1.1)) translateY(var(--#{v.$css-prefix}-bounce-height, -0.5em)); }
  50%  { transform: scale(var(--#{v.$css-prefix}-bounce-land-scale-x, 1.05),var(--#{v.$css-prefix}-bounce-land-scale-y, 0.95)) translateY(0); }
  57%  { transform: scale(1,1) translateY(var(--#{v.$css-prefix}-bounce-rebound, -0.125em)); }
  64%  { transform: scale(1,1) translateY(0); }
  100% { transform: scale(1,1) translateY(0); }
}

@keyframes #{v.$css-prefix}-fade {
  50% { opacity: var(--#{v.$css-prefix}-fade-opacity, 0.4); }
}

@keyframes #{v.$css-prefix}-beat-fade {
  0%, 100% {
    opacity: var(--#{v.$css-prefix}-beat-fade-opacity, 0.4);
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(var(--#{v.$css-prefix}-beat-fade-scale, 1.125));
  }
}

@keyframes #{v.$css-prefix}-flip {
  50% {
    transform: rotate3d(var(--#{v.$css-prefix}-flip-x, 0), var(--#{v.$css-prefix}-flip-y, 1), var(--#{v.$css-prefix}-flip-z, 0), var(--#{v.$css-prefix}-flip-angle, -180deg));
  }
}

@keyframes #{v.$css-prefix}-shake {
  0% { transform: rotate(-15deg); }
  4% { transform: rotate(15deg); }
  8%, 24% { transform: rotate(-18deg); }
  12%, 28% { transform: rotate(18deg); }
  16% { transform: rotate(-22deg); }
  20% { transform: rotate(22deg); }
  32% { transform: rotate(-12deg); }
  36% { transform: rotate(12deg); }
  40%, 100% { transform: rotate(0deg); }
}

@keyframes #{v.$css-prefix}-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
