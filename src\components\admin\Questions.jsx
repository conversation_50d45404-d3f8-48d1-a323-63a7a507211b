import { useEffect, useMemo, useState } from "react";
import { useNavigate } from "react-router-dom";
import api from "../../services/api";
import { Plus, Edit, Trash2, Filter } from "lucide-react";
import "../../styles/questions.css";

const Questions = () => {
  const navigate = useNavigate();
  const [questions, setQuestions] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");

  const [filters, setFilters] = useState({
    subject_id: "",
    type: "",
    search: "",
  });
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    total: 0,
  });

  useEffect(() => {
    const init = async () => {
      try {
        const res = await api.get("/admin/subjects");
        setSubjects(res.data.data || res.data || []);
      } catch (e) {
        console.error(e);
      }
    };
    init();
  }, []);

  useEffect(() => {
    fetchQuestions(pagination.current_page);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters]);

  const fetchQuestions = async (page = 1) => {
    setIsLoading(true);
    setError("");
    try {
      const params = { page };
      if (filters.subject_id) params.subject_id = filters.subject_id;
      if (filters.type) params.type = filters.type;
      // Backend doesn't support search param by default; kept for potential extension
      const res = await api.get("/admin/questions", { params });
      const data = res.data;
      setQuestions(data.data || []);
      setPagination({
        current_page: data.current_page,
        last_page: data.last_page,
        total: data.total,
      });
    } catch (e) {
      console.error("Error fetching questions:", e);
      setError("Failed to load questions");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (!window.confirm("Delete this question?")) return;
    try {
      await api.delete(`/admin/questions/${id}`);
      fetchQuestions(pagination.current_page);
    } catch (e) {
      console.error(e);
      setError("Failed to delete question");
    }
  };

  const canPrev = useMemo(() => pagination.current_page > 1, [pagination]);
  const canNext = useMemo(
    () => pagination.current_page < pagination.last_page,
    [pagination]
  );

  const gotoPage = (page) => {
    fetchQuestions(page);
    setPagination((p) => ({ ...p, current_page: page }));
  };

  return (
    <div className="questions-page">
      <div className="questions-header">
        <h1 className="questions-title">Questions Management</h1>
        <button
          onClick={() => navigate("/admin/questions/create")}
          className="btn btn-primary"
        >
          <Plus className="btn-icon" /> New Question
        </button>
      </div>

      <div className="filters-panel">
        <div className="filters-group">
          <div className="filter-item">
            <label className="filter-label">Subject</label>
            <select
              value={filters.subject_id}
              onChange={(e) =>
                setFilters((f) => ({ ...f, subject_id: e.target.value }))
              }
              className="filter-select"
            >
              <option value="">All</option>
              {subjects.map((s) => (
                <option key={s.id} value={s.id}>
                  {s.name}
                </option>
              ))}
            </select>
          </div>
          <div className="filter-item">
            <label className="filter-label">Type</label>
            <select
              value={filters.type}
              onChange={(e) =>
                setFilters((f) => ({ ...f, type: e.target.value }))
              }
              className="filter-select"
            >
              <option value="">All</option>
              <option value="mcq">Multiple Choice</option>
              <option value="true_false">True / False</option>
            </select>
          </div>
          <div className="filter-actions">
            <button
              onClick={() => fetchQuestions(1)}
              className="btn btn-secondary"
            >
              <Filter className="btn-icon" /> Apply Filters
            </button>
          </div>
        </div>
      </div>

      {error && <div className="alert alert-error">{error}</div>}

      {isLoading ? (
        <div className="loading-container">
          <div className="spinner"></div>
        </div>
      ) : questions.length === 0 ? (
        <div className="no-results">No questions found.</div>
      ) : (
        <div className="questions-table">
          <div className="table-header">
            <div className="table-cell cell-question">Question</div>
            <div className="table-cell cell-subject">Subject</div>
            <div className="table-cell cell-type">Type</div>
            <div className="table-cell cell-options">Options</div>
            <div className="table-cell cell-actions">Actions</div>
          </div>
          <div className="table-body">
            {questions.map((q) => (
              <div key={q.id} className="table-row">
                <div
                  className="table-cell cell-question"
                  title={q.question_text}
                >
                  {q.question_text}
                </div>
                <div className="table-cell cell-subject">
                  {q.subject?.name || "-"}
                </div>
                <div className="table-cell cell-type">
                  {q.type === "mcq" ? "Multiple Choice" : "True / False"}
                </div>
                <div className="table-cell cell-options">
                  {q.options?.length ?? 0}
                </div>
                <div className="table-cell cell-actions">
                  <button
                    onClick={() => navigate(`/admin/questions/${q.id}/edit`)}
                    className="btn btn-edit"
                    title="Edit"
                  >
                    <Edit className="btn-icon" />
                  </button>
                  <button
                    onClick={() => handleDelete(q.id)}
                    className="btn btn-delete"
                    title="Delete"
                  >
                    <Trash2 className="btn-icon" />
                  </button>
                </div>
              </div>
            ))}
          </div>
          <div className="pagination">
            <div className="pagination-info">
              Page {pagination.current_page} of {pagination.last_page} •{" "}
              {pagination.total} total
            </div>
            <div className="pagination-controls">
              <button
                disabled={!canPrev}
                onClick={() => canPrev && gotoPage(pagination.current_page - 1)}
                className={`btn btn-pagination ${!canPrev ? "disabled" : ""}`}
              >
                Prev
              </button>
              <button
                disabled={!canNext}
                onClick={() => canNext && gotoPage(pagination.current_page + 1)}
                className={`btn btn-pagination ${!canNext ? "disabled" : ""}`}
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Questions;
