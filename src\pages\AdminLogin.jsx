import { useState } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useDispatch, useSelector } from "react-redux";
import { loginUser } from "../store/slices/authSlice";
import "../styles/authForms.css";
import Logo from "../assets/logo.png";
import { toast } from "react-toastify";

const adminLoginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

const AdminLogin = () => {
  const [isLoading, setIsLoading] = useState(false);
  // const [error, setError] = useState("");
  const dispatch = useDispatch();
  const authError = useSelector((state) => state.auth.error);
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(adminLoginSchema),
  });

  const onSubmit = async (data) => {
    setIsLoading(true);
    // setError("");

    try {
      const result = await dispatch(
        loginUser({ email: data.email, password: data.password, type: "admin" })
      ).unwrap();
      navigate("/admin");
    } catch (error) {
      // setError(error);
      toast.error(error || "Something went wrong!");
    }

    setIsLoading(false);
  };

  return (
    <div className="auth-page">
      <div className="form-card">
        <div className="image">
          <img src={Logo} alt="login-logo" />
        </div>
        <div>
          <h2 className="form-title">Admin Sign In</h2>
          <p className="form-subtitle">Sign in to access the admin dashboard</p>
        </div>
        <form className="space-y-4" onSubmit={handleSubmit(onSubmit)}>
          <div className="form-group">
            <label htmlFor="email" className="form-label">
              Email address
            </label>
            <input
              {...register("email")}
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              className="form-control"
              placeholder="Email address"
            />
            {errors.email && (
              <p className="form-error">{errors.email.message}</p>
            )}
          </div>
          <div className="form-group">
            <label htmlFor="password" className="form-label">
              Password
            </label>
            <input
              {...register("password")}
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              className="form-control"
              placeholder="Password"
            />
            {errors.password && (
              <p className="form-error">{errors.password.message}</p>
            )}
          </div>

          {/* {error && <div className="form-error text-center">{error}</div>} */}

          <div className="form-actions">
            <button
              type="submit"
              disabled={isLoading}
              className="btn btn-block"
            >
              {isLoading ? "Signing in..." : "Sign in as Admin"}
            </button>
          </div>

          <div style={{ textAlign: "center" }}>
            <Link to="/">Back to home</Link>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminLogin;
