/*!
 * Font Awesome Free 7.0.1 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2025 Fonticons, Inc.
 */
@use "sass:string";
@use 'variables' as v;
@use 'mixins' as m;

:root, :host {
  --#{v.$css-prefix}-family-brands: 'Font Awesome 7 Brands';
  --#{v.$css-prefix}-font-brands: normal 400 1em/1 var(--#{v.$css-prefix}-family-brands);
}

@font-face {
  font-family: 'Font Awesome 7 Brands';
  font-style: normal;
  font-weight: 400;
  font-display: v.$font-display;
  src: url('#{v.$font-path}/fa-brands-400.woff2');
}

.fab,
.#{v.$css-prefix}-brands,
.#{v.$css-prefix}-classic.#{v.$css-prefix}-brands {
  --#{v.$css-prefix}-family: var(--#{v.$css-prefix}-family-brands);
  --#{v.$css-prefix}-style: 400;
}

@each $name, $icon in v.$brand-icons {
  .#{v.$css-prefix}-#{$name} {
    #{v.$icon-property}: string.unquote("\"#{ $icon }\"");
  }
}

// convenience mixin for declaring pseudo-elements by CSS variable,
// including all style-specific font properties and ::before elements.
@mixin icon($var) {
  @include m.fa-icon(Font Awesome 7 Brands);
  @extend .#{v.$css-prefix}-brands;

  &::before {
    content: string.unquote("\"#{ $var }\"");
  }
}
